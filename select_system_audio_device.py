"""
System Audio Device Selection Tool for RealtimeSTT

This tool helps you identify and select the correct system audio device
for capturing audio from your speakers/headphones.
"""

import time
import logging
from RealtimeSTT.system_audio_capture import SystemAudioCapture
from RealtimeSTT import AudioToTextRecorder

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def list_all_system_audio_devices():
    """List all available system audio devices with detailed information."""
    print("=" * 70)
    print("SYSTEM AUDIO DEVICE SELECTION TOOL")
    print("=" * 70)
    print()
    
    try:
        capture = SystemAudioCapture(debug_mode=True)
        devices = capture.get_available_loopback_devices()
        
        if not devices:
            print("❌ No system audio devices found!")
            return None
        
        print(f"Found {len(devices)} system audio loopback devices:")
        print()
        
        for i, device in enumerate(devices):
            print(f"Device {i}:")
            print(f"  Index: {device['index']}")
            print(f"  Name: {device['name']}")
            print(f"  Max Channels: {device['maxInputChannels']}")
            print(f"  Sample Rate: {device['defaultSampleRate']}")
            print(f"  Host API: {device['hostApi']}")
            print()
        
        # Show default device
        default_device = capture.get_default_loopback_device()
        if default_device:
            print(f"Current default device: {default_device['name']} (Index: {default_device['index']})")
        else:
            print("No default device found")
        
        capture.cleanup()
        return devices
        
    except Exception as e:
        print(f"❌ Error listing devices: {e}")
        return None

def test_device_audio_capture(device_index, device_name):
    """Test audio capture from a specific device."""
    print(f"\n🔍 Testing device: {device_name} (Index: {device_index})")
    print("Play some audio through your speakers and watch for audio levels...")
    print("Press Ctrl+C to stop testing this device")
    print("-" * 50)
    
    audio_data_count = 0
    max_level = 0
    
    def audio_callback(data):
        nonlocal audio_data_count, max_level
        audio_data_count += 1
        
        try:
            import numpy as np
            audio_array = np.frombuffer(data, dtype=np.int16)
            level = np.max(np.abs(audio_array)) if len(audio_array) > 0 else 0
            max_level = max(max_level, level)
            
            if audio_data_count % 25 == 0:  # Print every 25 chunks
                print(f"Audio chunks: {audio_data_count:4d}, Current level: {level:5d}, Max level: {max_level:5d}")
                
        except Exception as e:
            print(f"Error processing audio: {e}")
    
    try:
        capture = SystemAudioCapture(
            device_index=device_index,
            on_audio_data=audio_callback,
            debug_mode=True
        )
        
        if capture.start_capture():
            print("✓ Capture started - listening for audio...")
            
            try:
                time.sleep(10)  # Test for 10 seconds
            except KeyboardInterrupt:
                print("\nStopping test...")
            
            capture.stop_capture()
            
            print(f"\nTest Results for {device_name}:")
            print(f"  Total audio chunks: {audio_data_count}")
            print(f"  Maximum audio level: {max_level}")
            
            if audio_data_count == 0:
                print("  ❌ No audio data received")
                return False
            elif max_level < 100:
                print("  ⚠️  Very low audio levels detected")
                return False
            else:
                print("  ✅ Good audio levels detected!")
                return True
                
        else:
            print("❌ Failed to start capture")
            return False
            
    except Exception as e:
        print(f"❌ Error testing device: {e}")
        return False

def interactive_device_selection():
    """Interactive device selection and testing."""
    devices = list_all_system_audio_devices()
    if not devices:
        return None
    
    print("\n" + "=" * 70)
    print("DEVICE TESTING")
    print("=" * 70)
    
    while True:
        print(f"\nOptions:")
        print(f"  0-{len(devices)-1}: Test specific device")
        print(f"  a: Test all devices automatically")
        print(f"  q: Quit")
        
        choice = input("\nEnter your choice: ").strip().lower()
        
        if choice == 'q':
            break
        elif choice == 'a':
            print("\n🔄 Testing all devices automatically...")
            working_devices = []
            
            for i, device in enumerate(devices):
                result = test_device_audio_capture(device['index'], device['name'])
                if result:
                    working_devices.append((i, device))
                time.sleep(1)  # Brief pause between tests
            
            print(f"\n📊 Test Summary:")
            if working_devices:
                print(f"✅ Working devices found:")
                for i, device in working_devices:
                    print(f"  Device {i}: {device['name']} (Index: {device['index']})")
                
                if len(working_devices) == 1:
                    selected_device = working_devices[0][1]
                    print(f"\n🎯 Recommended device: {selected_device['name']} (Index: {selected_device['index']})")
                    return selected_device['index']
                else:
                    print(f"\n⚠️  Multiple working devices found. You may need to test manually to find the right one.")
            else:
                print(f"❌ No working devices found")
                
        elif choice.isdigit():
            device_idx = int(choice)
            if 0 <= device_idx < len(devices):
                device = devices[device_idx]
                result = test_device_audio_capture(device['index'], device['name'])
                if result:
                    print(f"\n🎯 Device {device_idx} appears to be working!")
                    use_device = input(f"Use this device? (y/n): ").strip().lower()
                    if use_device == 'y':
                        return device['index']
            else:
                print(f"Invalid device number. Please enter 0-{len(devices)-1}")
        else:
            print("Invalid choice. Please try again.")
    
    return None

if __name__ == "__main__":
    selected_device_index = interactive_device_selection()
    
    if selected_device_index is not None:
        print(f"\n🎉 Selected device index: {selected_device_index}")
        print(f"\nTo use this device in your RealtimeSTT configuration, set:")
        print(f"  'system_audio_device_index': {selected_device_index}")
    else:
        print(f"\n❌ No device selected")
