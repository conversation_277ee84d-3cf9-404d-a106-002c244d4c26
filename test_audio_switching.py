#!/usr/bin/env python3
"""
Simple test for audio source switching functionality.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

def test_audio_switching():
    """Test audio source switching functionality."""
    print("Testing audio source switching...")
    
    try:
        from RealtimeSTT import AudioToTextRecorder
        
        # Initialize recorder with microphone enabled
        recorder = AudioToTextRecorder(
            model='tiny.en',
            use_microphone=True,
            use_system_audio=False,
            spinner=False,
            level=40,  # ERROR level to reduce output
        )
        
        print(f"Initial source: {recorder.get_current_audio_source()}")
        
        # Test switching to system audio
        print("Switching to system audio...")
        recorder.set_system_audio(True)
        print(f"After set_system_audio(True): {recorder.get_current_audio_source()}")
        print(f"Microphone value: {recorder.use_microphone.value}")
        print(f"System audio value: {recorder.use_system_audio.value}")
        
        # Test switching back to microphone
        print("\nSwitching to microphone...")
        recorder.set_microphone(True)
        print(f"After set_microphone(True): {recorder.get_current_audio_source()}")
        print(f"Microphone value: {recorder.use_microphone.value}")
        print(f"System audio value: {recorder.use_system_audio.value}")
        
        # Test toggle
        print("\nTesting toggle...")
        recorder.toggle_audio_source()
        print(f"After toggle: {recorder.get_current_audio_source()}")
        print(f"Microphone value: {recorder.use_microphone.value}")
        print(f"System audio value: {recorder.use_system_audio.value}")
        
        recorder.shutdown()
        print("\n✓ Audio switching test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error in audio switching test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_audio_switching()
    sys.exit(0 if success else 1)
