#!/usr/bin/env python3
"""
Universal Voice Typing with RealtimeSTT

This application provides system-wide voice typing functionality.
Click into any text field in any application, start speaking, and
the transcribed text will automatically appear in that field.

Features:
- Real-time voice typing into any text field
- Works across all applications (browsers, editors, chat apps, etc.)
- Automatic text insertion at cursor position
- Smart punctuation and capitalization
- Hotkey support for start/stop
- Visual feedback in system tray

Requirements:
- pip install RealtimeSTT pyautogui pynput plyer
- Ollama running with gemma3:1b model (optional, falls back to Whisper)
"""

import sys
import time
import threading
from pathlib import Path

# Add parent directory for RealtimeSTT import
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    import pyautogui
    from pynput import keyboard
    import queue
    import re
    print("✓ All modules imported successfully")
except ImportError as e:
    print(f"✗ Missing dependency: {e}")
    print("\nInstall required packages:")
    print("pip install RealtimeSTT pyautogui pynput")
    sys.exit(1)

class UniversalVoiceTyping:
    def __init__(self):
        self.recorder = None
        self.is_listening = False
        self.text_queue = queue.Queue()
        self.last_typed_text = ""
        self.typing_enabled = True
        
        # Configure pyautogui for safety
        pyautogui.FAILSAFE = True  # Move mouse to corner to stop
        pyautogui.PAUSE = 0.01     # Small delay between keystrokes
        
        # Hotkey setup
        self.hotkey_listener = None
        self.setup_hotkeys()
        
        print("🎤 Universal Voice Typing initialized")
        print("📝 Click into any text field and start speaking!")
        
    def setup_hotkeys(self):
        """Setup global hotkeys for control."""
        def on_hotkey_toggle():
            """Toggle listening on/off with Ctrl+Shift+V"""
            if self.is_listening:
                self.stop_listening()
            else:
                self.start_listening()
        
        def on_hotkey_pause():
            """Pause/resume typing with Ctrl+Shift+P"""
            self.typing_enabled = not self.typing_enabled
            status = "enabled" if self.typing_enabled else "paused"
            print(f"🔄 Typing {status}")
        
        # Register hotkeys
        try:
            self.hotkey_listener = keyboard.GlobalHotKeys({
                '<ctrl>+<shift>+v': on_hotkey_toggle,
                '<ctrl>+<shift>+p': on_hotkey_pause,
            })
            self.hotkey_listener.start()
            print("⌨️  Hotkeys registered:")
            print("   Ctrl+Shift+V: Toggle listening")
            print("   Ctrl+Shift+P: Pause/resume typing")
        except Exception as e:
            print(f"⚠️  Could not register hotkeys: {e}")
    
    def clean_text_for_typing(self, text):
        """Clean and format text for typing."""
        if not text or not text.strip():
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Ensure proper capitalization
        if text and not text[0].isupper():
            text = text[0].upper() + text[1:]
        
        # Add space at the end if it doesn't end with punctuation
        if text and text[-1] not in '.!?':
            text += ' '
        
        return text
    
    def type_text_safely(self, text):
        """Safely type text into the active application."""
        if not text or not self.typing_enabled:
            return
        
        try:
            # Small delay to ensure the text field is ready
            time.sleep(0.05)
            
            # Type the text character by character for better compatibility
            for char in text:
                if not self.typing_enabled:  # Check if paused during typing
                    break
                pyautogui.write(char, interval=0.01)
            
            print(f"✅ Typed: {text.strip()}")
            
        except Exception as e:
            print(f"❌ Error typing text: {e}")
            print("💡 Make sure a text field is active and focused")
    
    def on_realtime_transcription(self, text):
        """Handle real-time transcription updates."""
        if not text or not text.strip():
            return
        
        # Clean the text
        clean_text = self.clean_text_for_typing(text)
        
        # Show live transcription in terminal
        print(f"\r🎤 Live: {clean_text}", end="", flush=True)
        
        # Don't type partial transcriptions to avoid clutter
        # Only type when we have a substantial update
        
    def on_final_transcription(self, text):
        """Handle final transcription - this gets typed."""
        if not text or not text.strip():
            return
        
        print(f"\n📝 Final: {text}")
        
        # Clean and format the text
        clean_text = self.clean_text_for_typing(text)
        
        if clean_text and clean_text != self.last_typed_text:
            # Add to typing queue
            self.text_queue.put(clean_text)
            self.last_typed_text = clean_text
    
    def text_typing_worker(self):
        """Worker thread that handles text typing."""
        while True:
            try:
                # Get text from queue (blocks until available)
                text = self.text_queue.get(timeout=1)
                
                if text == "STOP":
                    break
                
                # Type the text
                self.type_text_safely(text)
                
                # Mark task as done
                self.text_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in typing worker: {e}")
    
    def start_listening(self):
        """Start voice recognition and typing."""
        if self.is_listening:
            print("🎤 Already listening...")
            return
        
        try:
            print("🔄 Starting voice recognition...")
            
            # Configure recorder for optimal real-time performance
            self.recorder = AudioToTextRecorder(
                # Real-time transcription settings
                enable_realtime_transcription=True,
                realtime_model_type="tiny.en",
                realtime_processing_pause=0.1,
                
                # Ollama integration (with fallback)
                use_ollama=True,
                ollama_model="gemma3:1b",
                fallback_to_external=True,
                model="base",
                
                # Callbacks
                on_realtime_transcription_update=self.on_realtime_transcription,
                
                # Voice activity detection
                language="en",
                spinner=False,
                post_speech_silence_duration=1.0,
                min_length_of_recording=0.3,
                min_gap_between_recordings=0.2,
                
                # Performance settings
                silero_sensitivity=0.4,
                webrtc_sensitivity=3,
                beam_size_realtime=3,
                realtime_batch_size=8,
            )
            
            # Start typing worker thread
            self.typing_thread = threading.Thread(target=self.text_typing_worker, daemon=True)
            self.typing_thread.start()
            
            # Start listening thread
            self.listening_thread = threading.Thread(target=self.listening_worker, daemon=True)
            self.listening_thread.start()
            
            self.is_listening = True
            print("🟢 Voice typing active! Click into any text field and speak.")
            print("   Use Ctrl+Shift+V to stop, Ctrl+Shift+P to pause typing")
            
        except Exception as e:
            print(f"❌ Failed to start voice recognition: {e}")
            self.is_listening = False
    
    def listening_worker(self):
        """Worker thread for continuous listening."""
        try:
            while self.is_listening:
                # Get transcription
                text = self.recorder.text()
                
                if text and text.strip():
                    self.on_final_transcription(text)
                    
        except Exception as e:
            print(f"❌ Error in listening worker: {e}")
            self.is_listening = False
    
    def stop_listening(self):
        """Stop voice recognition."""
        if not self.is_listening:
            print("🎤 Not currently listening...")
            return
        
        print("\n🔄 Stopping voice recognition...")
        self.is_listening = False
        
        # Stop typing worker
        self.text_queue.put("STOP")
        
        # Shutdown recorder
        if self.recorder:
            try:
                self.recorder.shutdown()
                self.recorder = None
            except:
                pass
        
        print("🔴 Voice typing stopped")
    
    def run(self):
        """Main application loop."""
        print("=" * 60)
        print("🎤 Universal Voice Typing with RealtimeSTT")
        print("=" * 60)
        print("This app will type your speech into any active text field!")
        print()
        print("Instructions:")
        print("1. Click into any text field (browser, editor, chat, etc.)")
        print("2. Press Ctrl+Shift+V to start voice typing")
        print("3. Start speaking - text will appear where your cursor is")
        print("4. Press Ctrl+Shift+V again to stop")
        print()
        print("Controls:")
        print("  Ctrl+Shift+V: Start/Stop voice typing")
        print("  Ctrl+Shift+P: Pause/Resume typing")
        print("  Ctrl+C: Exit application")
        print("=" * 60)
        print()
        
        try:
            # Keep the main thread alive
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
            self.stop_listening()
            
            if self.hotkey_listener:
                self.hotkey_listener.stop()
            
            print("✅ Universal Voice Typing stopped")

def main():
    """Main entry point."""
    # Check dependencies
    try:
        import pyautogui
        import pynput
        print("✓ All dependencies available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\nInstall required packages:")
        print("pip install pyautogui pynput")
        return 1
    
    # Create and run the application
    app = UniversalVoiceTyping()
    app.run()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
