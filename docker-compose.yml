services:
  rtstt:
    build:
      context: .
      target: gpu # or cpu
    image: rtstt
    container_name: rtstt
    volumes:
      # - ./RealtimeSTT:/app/RealtimeSTT
      - cache:/root/.cache
      - ollama_models:/root/.ollama  # Persist Ollama models
    ports:
      - "9001:9001"
      - "11434:11434"  # Ollama API port
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_MODELS=/root/.ollama/models
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # if 'gpu' target
    deploy:
      resources:
        reservations:
          devices:
          - capabilities: ["gpu"]
  nginx:
    image: nginx:latest
    container_name: nginx_web
    ports:
      - "8081:80"
    volumes:
      - ./example_browserclient:/usr/share/nginx/html

volumes:
  cache:
  ollama_models: