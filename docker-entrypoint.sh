#!/bin/bash

# Docker entrypoint script for RealtimeSTT with Ollama integration
# This script starts Ollama server and then the RealtimeSTT application

set -e

echo "Starting RealtimeSTT with Ollama integration..."

# Start Ollama server in the background
echo "Starting Ollama server..."
ollama serve &
OLLAMA_PID=$!

# Wait for Ollama to be ready
echo "Waiting for Ollama server to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "Ollama server is ready!"
        break
    fi
    echo "Waiting for Ollama... ($i/30)"
    sleep 2
done

# Check if Ollama is actually running
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "Warning: Ollama server failed to start properly"
    echo "Application will run with Whisper fallback only"
else
    echo "Ollama server is running successfully"
    
    # Pull the default model if it doesn't exist
    echo "Checking for gemma3:1b model..."
    if ! ollama list | grep -q "gemma3:1b"; then
        echo "Pulling gemma3:1b model (this may take a while)..."
        ollama pull gemma3:1b || echo "Warning: Failed to pull gemma3:1b model"
    else
        echo "gemma3:1b model is already available"
    fi
fi

# Function to cleanup on exit
cleanup() {
    echo "Shutting down..."
    if [ ! -z "$OLLAMA_PID" ]; then
        kill $OLLAMA_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start the main application
echo "Starting RealtimeSTT application..."
cd /app
python3 example_browserclient/server.py &
APP_PID=$!

# Wait for either process to exit
wait $APP_PID
