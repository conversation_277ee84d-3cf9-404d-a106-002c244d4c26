#!/usr/bin/env python3
"""
Quick Live Transcription Demo - Just like in the videos!

This is the simplest way to get real-time speech transcription working.
Just run this script and start talking - you'll see live updates!
"""

from RealtimeSTT import AudioToTextRecorder

def live_transcription_callback(text):
    """This function gets called with live transcription updates"""
    print(f"\r🎤 {text}", end="", flush=True)

def final_transcription_callback(text):
    """This function gets called with the final transcription"""
    if text.strip():
        print(f"\n✅ {text}")
        print("-" * 40)

if __name__ == '__main__':
    print("🎤 Live Speech Transcription Demo")
    print("=" * 40)
    print("Start speaking and watch the live transcription!")
    print("Press Ctrl+C to stop")
    print("=" * 40)
    print()
    
    # Simple configuration for live transcription
    recorder = AudioToTextRecorder(
        # Enable real-time transcription
        enable_realtime_transcription=True,
        realtime_model_type="tiny.en",
        realtime_processing_pause=0.1,
        
        # Ollama settings (will fallback to Whisper if not available)
        use_ollama=True,
        ollama_model="gemma3:1b",
        fallback_to_external=True,
        model="base",  # Fallback Whisper model
        
        # Callbacks for live updates
        on_realtime_transcription_update=live_transcription_callback,
        
        # Basic settings
        language="en",
        spinner=False,  # Disable spinner for cleaner output
        post_speech_silence_duration=1.0,
    )
    
    print("🟢 Ready! Start speaking...")
    print()
    
    try:
        while True:
            # Get final transcription (live updates happen via callback)
            final_text = recorder.text()
            if final_text.strip():
                final_transcription_callback(final_text)
                
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping...")
    finally:
        recorder.shutdown()
        print("✅ Done!")
