# RealtimeSTT Project Cleanup Plan

## Overview
This plan outlines the systematic cleanup of the RealtimeSTT project to focus on core functionality: Ollama integration, microphone transcription, audio file transcription, and audio source transcription. The cleanup will remove external API dependencies and unnecessary components while preserving essential features.

## Current State Analysis

### Core Components to Preserve
- **RealtimeSTT/audio_recorder.py** - Main transcription engine with Ollama integration
- **RealtimeSTT/ollama_client.py** - Local Ollama client wrapper
- **RealtimeSTT/audio_input.py** - Microphone input handling
- **RealtimeSTT/system_audio_capture.py** - System audio capture for live sources
- **RealtimeSTT_server/** - WebSocket server and client for remote transcription
- **realtime_transcription_demo.py** - Core demo showcasing Ollama integration

### External API Dependencies Identified
- **OpenAI API** - Used in multiple test files and examples
- **Azure Speech API** - Text-to-speech integration in examples
- **ElevenLabs API** - Voice synthesis in advanced examples
- **RealtimeTTS** - External text-to-speech library dependency

### Files Requiring Removal/Cleanup
- **example_app/** - Contains OpenAI/Azure integrations
- **tests/** - Multiple files with external API dependencies
- **example_browserclient/** - May contain external dependencies
- **example_webserver/** - May contain external dependencies
- **design_docs/** - Configuration schemas for external APIs

## Phase-by-Phase Cleanup Plan

### Phase 1: Analysis and Preparation
**Objective**: Complete inventory and backup preparation

#### 1.1 Dependency Analysis
- [ ] Create complete inventory of all Python files and their external dependencies
- [ ] Map all import statements to identify external API usage
- [ ] Document current functionality that will be preserved vs removed
- [ ] Identify all environment variable usage for API keys

#### 1.2 Backup and Safety
- [ ] Create backup of current codebase state
- [ ] Verify git repository is clean and committed
- [ ] Document current working functionality for regression testing

#### 1.3 Requirements Analysis
- [ ] Analyze requirements.txt for dependencies that can be removed
- [ ] Identify core dependencies needed for Ollama + audio functionality
- [ ] Plan new minimal requirements.txt

### Phase 2: File Classification and Removal
**Objective**: Remove unnecessary files and directories

#### 2.1 Remove External API Integration Files
- [ ] Remove `example_app/` directory (contains OpenAI/Azure integrations)
- [ ] Remove `tests/openai_voice_interface.py`
- [ ] Remove `tests/advanced_talk.py` 
- [ ] Remove `tests/minimalistic_talkbot.py`
- [ ] Remove `tests/realtimestt_speechendpoint.py`
- [ ] Remove `tests/translator.py`
- [ ] Remove files in `tests/` that import `openai`, `RealtimeTTS`, or external APIs

#### 2.2 Remove Design Documentation for External APIs
- [ ] Remove `design_docs/configuration_schema_design.md` (contains external API schemas)
- [ ] Remove `design_docs/fallback_mechanism_design.md` (if focused on external APIs)
- [ ] Keep `design_docs/ollama_client_wrapper_design.md`
- [ ] Keep `design_docs/local_model_management_design.md`

#### 2.3 Remove Example Directories with External Dependencies
- [ ] Analyze and remove `example_browserclient/` if it contains external API usage
- [ ] Analyze and remove `example_webserver/` if it contains external API usage
- [ ] Keep directories that only use core RealtimeSTT functionality

#### 2.4 Remove Installation Scripts with External Dependencies
- [ ] Remove `example_app/install_cpu.bat` (installs openai package)
- [ ] Remove `example_app/install_gpu.bat` (installs openai package)
- [ ] Remove `example_app/start.bat` (configures external API keys)

### Phase 3: Code Cleanup and Refactoring
**Objective**: Clean up remaining code to remove external API references

#### 3.1 Clean Core Audio Recorder
- [ ] Review `RealtimeSTT/audio_recorder.py` for any external API fallback code
- [ ] Remove any OpenAI/Azure/ElevenLabs client initialization code
- [ ] Ensure Ollama integration remains intact
- [ ] Remove external API configuration parameters

#### 3.2 Clean Configuration and Migration Code
- [ ] Review `RealtimeSTT/config_migration.py` for external API key handling
- [ ] Remove external API key validation and migration logic
- [ ] Keep Ollama-specific configuration handling

#### 3.3 Update Server Components
- [ ] Review `RealtimeSTT_server/stt_server.py` for external API references
- [ ] Remove any external API fallback mechanisms
- [ ] Ensure WebSocket functionality remains for core transcription

#### 3.4 Clean Demo and Utility Scripts
- [ ] Update `realtime_transcription_demo.py` to remove any external API fallbacks
- [ ] Clean `universal_voice_typing.py` to focus on Ollama + Whisper fallback only
- [ ] Remove external API configuration from remaining demo scripts

### Phase 4: Dependencies and Documentation Cleanup
**Objective**: Update dependencies and documentation

#### 4.1 Update Requirements
- [ ] Create new minimal `requirements.txt` with only essential dependencies:
  - PyAudio, PyAudioWPatch (audio input)
  - faster-whisper (local transcription fallback)
  - torch, torchaudio (ML dependencies)
  - websockets (server functionality)
  - Core audio processing libraries (scipy, soundfile, etc.)
- [ ] Remove dependencies: openai, azure-cognitiveservices-speech, elevenlabs
- [ ] Update `requirements-gpu.txt` accordingly

#### 4.2 Update Setup Configuration
- [ ] Update `setup.py` to reflect new minimal dependencies
- [ ] Update package description to emphasize local-first approach
- [ ] Update keywords to remove external API references

#### 4.3 Update Documentation
- [ ] Update main `README.md` to focus on Ollama integration and local functionality
- [ ] Remove references to external API setup and configuration
- [ ] Update `OLLAMA_SETUP_GUIDE.md` to be the primary setup documentation
- [ ] Update `RealtimeSTT_server/README.md` to remove external API examples

#### 4.4 Update Docker Configuration
- [ ] Review `Dockerfile` and `docker-compose.yml` for external API dependencies
- [ ] Ensure Docker setup focuses on Ollama + core functionality
- [ ] Remove any external API key environment variable configurations

### Phase 5: Testing and Validation
**Objective**: Ensure cleaned codebase works correctly

#### 5.1 Core Functionality Testing
- [ ] Test Ollama integration with `realtime_transcription_demo.py`
- [ ] Test microphone transcription functionality
- [ ] Test audio file transcription capabilities
- [ ] Test system audio capture functionality

#### 5.2 Server Functionality Testing
- [ ] Test WebSocket server startup and client connections
- [ ] Verify real-time transcription through WebSocket interface
- [ ] Test CLI client functionality

#### 5.3 Installation Testing
- [ ] Test fresh installation with new requirements.txt
- [ ] Verify all core functionality works without external API dependencies
- [ ] Test Docker container build and functionality

### Phase 6: Final Documentation and Cleanup
**Objective**: Complete the cleanup with updated documentation

#### 6.1 Update Core Documentation
- [ ] Rewrite main README.md to focus on local-first architecture
- [ ] Create simple setup guide emphasizing Ollama installation
- [ ] Document the four core features clearly
- [ ] Remove all external API setup instructions

#### 6.2 Clean Up Remaining Files
- [ ] Remove `todo.md` if it contains external API tasks
- [ ] Clean up any remaining configuration templates with external API references
- [ ] Remove any remaining batch files or scripts with external dependencies

#### 6.3 Final Validation
- [ ] Perform final test of all core functionality
- [ ] Verify no external API dependencies remain in codebase
- [ ] Confirm minimal, focused codebase meets requirements

## Expected Outcome

After cleanup, the project will have:

### Preserved Core Features
1. **Ollama Integration** - Full local model support with automatic fallback to Whisper
2. **Microphone Transcription** - Real-time voice-to-text from microphone input
3. **Audio File Transcription** - Support for transcribing various audio file formats
4. **Audio Source Transcription** - System audio capture for live sources (meetings, videos, etc.)

### Removed Components
- All external AI API integrations (OpenAI, Azure, ElevenLabs)
- Complex example applications with external dependencies
- API key handling and authentication systems
- Text-to-speech functionality (RealtimeTTS dependency)
- Non-essential test files and examples

### Simplified Architecture
- Local-first approach with Ollama as primary transcription engine
- Whisper as fallback when Ollama unavailable
- Minimal dependency footprint
- Clear, focused documentation
- Streamlined installation process

This cleanup will result in a lean, focused codebase that delivers the four core features without external API dependencies or unnecessary complexity.
