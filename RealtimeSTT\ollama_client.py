"""
Ollama Client Wrapper for RealtimeSTT

This module provides a unified interface for interacting with local Ollama models,
following the existing OpenAI client pattern found in realtimestt_speechendpoint.py.
"""

import logging
import time
import requests
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

logger = logging.getLogger("realtimestt.ollama")


class OllamaClient:
    """
    Wrapper class for Ollama client providing unified interface for local model interactions.
    Handles connection management, model loading/switching, streaming responses, and error handling.
    """
    
    def __init__(self, 
                 host: str = "localhost:11434",
                 model: str = "gemma3:1b", 
                 timeout: float = 30.0,
                 max_retries: int = 3,
                 fallback_client: Optional[Any] = None):
        """
        Initialize Ollama client with configuration parameters.
        
        Args:
            host: Ollama server host and port
            model: Default model to use
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            fallback_client: Fallback client for external APIs
        """
        self.host = host
        self.base_url = f"http://{host}"
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.fallback_client = fallback_client
        
        # Health monitoring
        self.last_health_check = None
        self.is_healthy = False
        self.consecutive_failures = 0
        
        # Model cache
        self.available_models = []
        self.model_cache_time = None
        self.cache_duration = timedelta(minutes=5)

        # Response cache for improved performance
        self.response_cache = {}
        self.response_cache_max_size = 100
        self.response_cache_ttl = timedelta(minutes=10)
        
        logger.info(f"Initialized OllamaClient with host: {host}, model: {model}")
        
        # Perform initial health check
        self.health_check()
    
    def health_check(self) -> bool:
        """
        Perform health check on Ollama server.
        
        Returns:
            bool: True if server is healthy, False otherwise
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.is_healthy = True
                self.consecutive_failures = 0
                self.last_health_check = datetime.now()
                logger.debug("Ollama health check passed")
                return True
        except Exception as e:
            logger.warning(f"Ollama health check failed: {e}")
        
        self.is_healthy = False
        self.consecutive_failures += 1
        return False
    
    def is_available(self) -> bool:
        """
        Check if Ollama is available and healthy.
        
        Returns:
            bool: True if available, False otherwise
        """
        # Check if we need to refresh health status
        if (self.last_health_check is None or 
            datetime.now() - self.last_health_check > timedelta(minutes=1)):
            return self.health_check()
        
        return self.is_healthy
    
    def list_models(self) -> List[str]:
        """
        Get list of available models from Ollama.
        
        Returns:
            List[str]: List of available model names
        """
        # Check cache first
        if (self.model_cache_time and 
            datetime.now() - self.model_cache_time < self.cache_duration):
            return self.available_models
        
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                self.available_models = [model['name'] for model in data.get('models', [])]
                self.model_cache_time = datetime.now()
                logger.debug(f"Retrieved {len(self.available_models)} models from Ollama")
                return self.available_models
        except Exception as e:
            logger.error(f"Failed to list models: {e}")
        
        return []
    
    def pull_model(self, model_name: str) -> bool:
        """
        Download/pull a model from Ollama registry.
        
        Args:
            model_name: Name of the model to pull
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Pulling model: {model_name}")
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                timeout=300,  # Longer timeout for model downloads
                stream=True
            )
            
            if response.status_code == 200:
                # Process streaming response
                for line in response.iter_lines():
                    if line:
                        data = json.loads(line)
                        if data.get('status') == 'success':
                            logger.info(f"Successfully pulled model: {model_name}")
                            # Refresh model cache
                            self.model_cache_time = None
                            return True
                        elif 'error' in data:
                            logger.error(f"Error pulling model {model_name}: {data['error']}")
                            return False
        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
        
        return False
    
    def switch_model(self, model_name: str) -> bool:
        """
        Switch to a different model.
        
        Args:
            model_name: Name of the model to switch to
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Check if model is available
        available_models = self.list_models()
        if model_name not in available_models:
            logger.warning(f"Model {model_name} not available. Attempting to pull...")
            if not self.pull_model(model_name):
                return False
        
        # Test the model with a simple request
        try:
            test_response = self.chat_completions_create(
                model=model_name,
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            if test_response:
                self.model = model_name
                logger.info(f"Successfully switched to model: {model_name}")
                return True
        except Exception as e:
            logger.error(f"Failed to switch to model {model_name}: {e}")
        
        return False
    
    def _retry_request(self, func, *args, **kwargs):
        """
        Execute a function with retry logic and exponential backoff.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result or None if all retries failed
        """
        for attempt in range(self.max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {wait_time}s...")
                if attempt < self.max_retries - 1:
                    time.sleep(wait_time)
                else:
                    logger.error(f"All {self.max_retries} attempts failed")
                    raise
        
        return None

    def chat_completions_create(self, model: str = None, messages: List[Dict] = None, **kwargs) -> Dict:
        """
        Create chat completion using Ollama API, compatible with OpenAI format.

        Args:
            model: Model name to use (defaults to self.model)
            messages: List of message dictionaries
            **kwargs: Additional parameters

        Returns:
            Dict: Response in OpenAI-compatible format
        """
        if not self.is_available():
            if self.fallback_client:
                logger.info("Ollama unavailable, using fallback client")
                return self.fallback_client.chat.completions.create(
                    model=model or "gpt-3.5-turbo",
                    messages=messages,
                    **kwargs
                )
            else:
                raise Exception("Ollama is not available and no fallback client configured")

        model = model or self.model

        # Check cache first (only for non-streaming requests)
        if not kwargs.get("stream", False):
            cache_key = self._get_cache_key(model, messages or [])
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response

        # Convert OpenAI format to Ollama format
        ollama_request = {
            "model": model,
            "messages": messages or [],
            "stream": kwargs.get("stream", False),
            "options": {}
        }

        # Map common parameters
        if "max_tokens" in kwargs:
            ollama_request["options"]["num_predict"] = kwargs["max_tokens"]
        if "temperature" in kwargs:
            ollama_request["options"]["temperature"] = kwargs["temperature"]
        if "top_p" in kwargs:
            ollama_request["options"]["top_p"] = kwargs["top_p"]

        def _make_request():
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=ollama_request,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()

        try:
            ollama_response = self._retry_request(_make_request)

            # Convert Ollama response to OpenAI format
            openai_response = {
                "id": f"chatcmpl-{int(time.time())}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": ollama_response.get("message", {}).get("content", "")
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": ollama_response.get("prompt_eval_count", 0),
                    "completion_tokens": ollama_response.get("eval_count", 0),
                    "total_tokens": ollama_response.get("prompt_eval_count", 0) + ollama_response.get("eval_count", 0)
                }
            }

            self.consecutive_failures = 0

            # Cache the response if not streaming
            if not kwargs.get("stream", False):
                self._cache_response(cache_key, openai_response)

            return openai_response

        except Exception as e:
            self.consecutive_failures += 1
            logger.error(f"Ollama request failed: {e}")

            # Use fallback if available and we've had multiple failures
            if self.fallback_client and self.consecutive_failures >= 2:
                logger.info("Using fallback client due to repeated Ollama failures")
                return self.fallback_client.chat.completions.create(
                    model=model if model.startswith("gpt") else "gpt-3.5-turbo",
                    messages=messages,
                    **kwargs
                )
            else:
                raise

    def ensure_model_available(self, model_name: str = None) -> bool:
        """
        Ensure a model is available, downloading if necessary.

        Args:
            model_name: Name of the model to ensure (defaults to self.model)

        Returns:
            bool: True if model is available, False otherwise
        """
        model_name = model_name or self.model

        # Check if model is already available
        available_models = self.list_models()
        if model_name in available_models:
            return True

        # Try to pull the model
        logger.info(f"Model {model_name} not found locally, attempting to download...")
        return self.pull_model(model_name)

    def get_model_info(self, model_name: str = None) -> Dict:
        """
        Get information about a specific model.

        Args:
            model_name: Name of the model (defaults to self.model)

        Returns:
            Dict: Model information
        """
        model_name = model_name or self.model

        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": model_name},
                timeout=self.timeout
            )
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            logger.error(f"Failed to get model info for {model_name}: {e}")

        return {}

    def _get_cache_key(self, model: str, messages: List[Dict]) -> str:
        """
        Generate a cache key for the request.

        Args:
            model: Model name
            messages: List of messages

        Returns:
            str: Cache key
        """
        import hashlib
        content = f"{model}:{str(messages)}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[Dict]:
        """
        Get cached response if available and not expired.

        Args:
            cache_key: Cache key

        Returns:
            Dict or None: Cached response if available
        """
        if cache_key in self.response_cache:
            cached_item = self.response_cache[cache_key]
            if datetime.now() - cached_item['timestamp'] < self.response_cache_ttl:
                logger.debug(f"Cache hit for key: {cache_key[:8]}...")
                return cached_item['response']
            else:
                # Remove expired item
                del self.response_cache[cache_key]

        return None

    def _cache_response(self, cache_key: str, response: Dict):
        """
        Cache a response.

        Args:
            cache_key: Cache key
            response: Response to cache
        """
        # Clean up cache if it's getting too large
        if len(self.response_cache) >= self.response_cache_max_size:
            # Remove oldest entries
            sorted_items = sorted(
                self.response_cache.items(),
                key=lambda x: x[1]['timestamp']
            )
            for key, _ in sorted_items[:self.response_cache_max_size // 2]:
                del self.response_cache[key]

        self.response_cache[cache_key] = {
            'response': response,
            'timestamp': datetime.now()
        }
        logger.debug(f"Cached response for key: {cache_key[:8]}...")

    def clear_cache(self):
        """Clear the response cache."""
        self.response_cache.clear()
        logger.info("Response cache cleared")

    def get_cache_stats(self) -> Dict:
        """
        Get cache statistics.

        Returns:
            Dict: Cache statistics
        """
        return {
            'cache_size': len(self.response_cache),
            'max_size': self.response_cache_max_size,
            'ttl_minutes': self.response_cache_ttl.total_seconds() / 60
        }
