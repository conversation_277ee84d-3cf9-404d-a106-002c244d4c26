#!/usr/bin/env python3
"""
Simple Universal Voice Typing

Click into any text field, run this script, and start speaking.
Your speech will be automatically typed into the active text field.

Usage:
1. Run: python simple_voice_typing.py
2. Click into any text field (browser, editor, chat app, etc.)
3. Start speaking - text appears where your cursor is!
4. Press Ctrl+C to stop
"""

import sys
import time
from pathlib import Path

# Add parent directory for RealtimeSTT import
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    import pyautogui
    print("✓ Ready for universal voice typing!")
except ImportError as e:
    print(f"❌ Missing dependency: {e}")
    print("\nInstall required packages:")
    print("pip install RealtimeSTT pyautogui")
    print("\nFor better compatibility, also install:")
    print("pip install pynput")
    sys.exit(1)

class SimpleVoiceTyping:
    def __init__(self):
        # Configure pyautogui for safety and speed
        pyautogui.FAILSAFE = True  # Move mouse to top-left corner to emergency stop
        pyautogui.PAUSE = 0.01     # Small delay between actions
        
        self.last_text = ""
        
    def type_text(self, text):
        """Type text into the currently focused application."""
        if not text or not text.strip():
            return
            
        # Clean up the text
        clean_text = text.strip()
        
        # Avoid typing the same text twice
        if clean_text == self.last_text:
            return
            
        try:
            # Add a space at the end for natural typing
            if not clean_text.endswith((' ', '.', '!', '?', ',')):
                clean_text += ' '
            
            # Type the text
            pyautogui.write(clean_text, interval=0.005)  # Fast typing
            
            print(f"✅ Typed: {clean_text.strip()}")
            self.last_text = text.strip()
            
        except Exception as e:
            print(f"❌ Error typing: {e}")
            print("💡 Make sure a text field is active and has focus")
    
    def on_transcription(self, text):
        """Called when speech is transcribed."""
        if text and text.strip():
            print(f"🎤 Heard: {text}")
            self.type_text(text)
    
    def run(self):
        """Start the voice typing system."""
        print("🎤 Simple Universal Voice Typing")
        print("=" * 40)
        print("1. Click into ANY text field (browser, editor, chat, etc.)")
        print("2. Start speaking")
        print("3. Watch your speech appear as text!")
        print("4. Press Ctrl+C to stop")
        print("=" * 40)
        print()
        
        # Wait for user to get ready
        input("Press Enter when you're ready to start voice typing...")
        print()
        
        try:
            # Create recorder with optimal settings for typing
            recorder = AudioToTextRecorder(
                # Basic settings
                model="base",  # Good balance of speed and accuracy
                language="en",
                use_microphone=True,
                spinner=False,  # Don't clutter output
                
                # Ollama integration (optional)
                use_ollama=True,
                ollama_model="gemma3:1b",
                fallback_to_external=True,
                
                # Voice detection settings
                post_speech_silence_duration=1.0,  # Wait 1 second after speech
                min_length_of_recording=0.5,       # Minimum 0.5 seconds
                min_gap_between_recordings=0.3,    # Small gap between recordings
                silero_sensitivity=0.4,            # Good sensitivity
                
                # Performance settings
                beam_size=5,
                batch_size=16,
            )
            
            print("🟢 Voice typing is ACTIVE!")
            print("💬 Click into a text field and start speaking...")
            print("🛑 Press Ctrl+C to stop")
            print()
            
            # Main loop - continuously listen and type
            while True:
                try:
                    # Get transcription
                    text = recorder.text()
                    
                    # Type it if we got something
                    if text and text.strip():
                        self.on_transcription(text)
                        
                except KeyboardInterrupt:
                    print("\n🛑 Stopping voice typing...")
                    break
                except Exception as e:
                    print(f"⚠️  Error: {e}")
                    print("Continuing...")
                    continue
            
        except Exception as e:
            print(f"❌ Failed to start: {e}")
            print("\nTroubleshooting:")
            print("1. Check microphone permissions")
            print("2. Make sure Ollama is running: ollama serve")
            print("3. Try: ollama pull gemma3:1b")
            return 1
        
        finally:
            # Clean shutdown
            try:
                recorder.shutdown()
                print("✅ Voice typing stopped cleanly")
            except:
                pass
        
        return 0

def main():
    """Main entry point."""
    print("Checking dependencies...")
    
    # Check if pyautogui works
    try:
        import pyautogui
        # Test basic functionality
        pyautogui.size()  # This will fail if GUI not available
        print("✓ pyautogui is working")
    except Exception as e:
        print(f"❌ pyautogui issue: {e}")
        print("\nThis might be because:")
        print("- You're running in a headless environment")
        print("- GUI permissions are not granted")
        print("- Display is not available")
        return 1
    
    # Check RealtimeSTT
    try:
        from RealtimeSTT import AudioToTextRecorder
        print("✓ RealtimeSTT is available")
    except ImportError as e:
        print(f"❌ RealtimeSTT issue: {e}")
        print("Install with: pip install RealtimeSTT")
        return 1
    
    # Run the application
    app = SimpleVoiceTyping()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
