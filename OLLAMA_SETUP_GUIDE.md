# RealtimeSTT with Ollama - Complete Setup Guide

This comprehensive guide will help you set up and run RealtimeSTT with local Ollama models for speech-to-text transcription. No external API keys required!

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Installation Steps](#installation-steps)
3. [Configuration](#configuration)
4. [Running the Application](#running-the-application)
5. [Verification](#verification)
6. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, recommended 8GB+ for optimal performance
- **Storage**: At least 5GB free space for models
- **Python**: Version 3.8 or higher
- **Microphone**: Working microphone for audio input

### Required Software
1. **Python 3.8+** - [Download from python.org](https://www.python.org/downloads/)
2. **Ollama** - [Download from ollama.ai](https://ollama.ai)
3. **Git** (optional) - For cloning the repository

## Installation Steps

### Step 1: Install Ollama

#### Windows
1. Visit [https://ollama.ai](https://ollama.ai)
2. Click "Download for Windows"
3. Run the installer and follow the setup wizard
4. Ollama will be installed and automatically start as a service

#### macOS
```bash
# Using Homebrew (recommended)
brew install ollama

# Or download from website
# Visit https://ollama.ai and download the macOS installer
```

#### Linux
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
sudo systemctl start ollama
sudo systemctl enable ollama
```

### Step 2: Start Ollama Server

Open a terminal/command prompt and run:
```bash
ollama serve
```

**Note**: Keep this terminal open. Ollama needs to run in the background.

### Step 3: Download the AI Model

In a new terminal/command prompt, download the recommended model:
```bash
# Download the lightweight gemma3:1b model (recommended for beginners)
ollama pull gemma3:1b

# Alternative models (optional):
# ollama pull llama3.2:1b    # Even smaller model
# ollama pull llama3.2:3b    # Larger, more accurate model
```

### Step 4: Install Python Dependencies

#### Option A: Install from PyPI (Recommended)
```bash
# Install RealtimeSTT
pip install RealtimeSTT

# Install additional dependencies for Ollama integration
pip install requests websockets
```

#### Option B: Install from Source
```bash
# Clone the repository
git clone https://github.com/KoljaB/RealtimeSTT.git
cd RealtimeSTT

# Install in development mode
pip install -e .

# Install additional dependencies
pip install requests websockets
```

### Step 5: Platform-Specific Audio Dependencies

#### Windows
```bash
# Install Windows-specific audio dependencies
pip install pyaudio
```

#### macOS
```bash
# Install PortAudio first
brew install portaudio

# Then install PyAudio
pip install pyaudio
```

#### Linux (Ubuntu/Debian)
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install python3-dev portaudio19-dev

# Install PyAudio
pip install pyaudio
```

### Step 6: GPU Support (Optional but Recommended)

For better performance, install CUDA support:

#### CUDA 12.x
```bash
pip install torch==2.5.1+cu121 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu121
```

#### CUDA 11.8
```bash
pip install torch==2.5.1+cu118 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu118
```

## Configuration

### Basic Configuration

Create a Python script with the following configuration:

```python
from RealtimeSTT import AudioToTextRecorder

# Basic Ollama configuration
config = {
    # Core settings
    'model': 'gemma3:1b',
    'language': 'en',
    'use_microphone': True,
    'spinner': True,
    
    # Ollama integration
    'use_ollama': True,
    'ollama_model': 'gemma3:1b',
    'ollama_host': 'localhost:11434',
    'ollama_timeout': 30.0,
    'ollama_auto_download': True,
    'fallback_to_external': True,
    
    # Fallback Whisper model (if Ollama fails)
    'whisper_fallback_model': 'base',
    
    # Voice activity detection
    'silero_sensitivity': 0.4,
    'webrtc_sensitivity': 3,
    'post_speech_silence_duration': 0.6,
    'min_length_of_recording': 0.5,
}

# Initialize recorder
recorder = AudioToTextRecorder(**config)
```

### Advanced Configuration

For more advanced usage, you can enable real-time transcription:

```python
advanced_config = {
    # ... (include basic config above)
    
    # Real-time transcription
    'enable_realtime_transcription': True,
    'realtime_model_type': 'tiny.en',
    'realtime_processing_pause': 0.02,
    
    # Performance tuning
    'beam_size': 5,
    'batch_size': 16,
    'early_transcription_on_silence': 200,
}
```

## Running the Application

### Method 1: Quick Test Script

Create a file called `test_ollama.py`:

```python
#!/usr/bin/env python3
from RealtimeSTT import AudioToTextRecorder

def main():
    print("Initializing RealtimeSTT with Ollama...")
    
    # Configure for Ollama
    recorder = AudioToTextRecorder(
        use_ollama=True,
        ollama_model='gemma3:1b',
        ollama_host='localhost:11434',
        fallback_to_external=True,
        model='base',  # Fallback Whisper model
        language='en',
        spinner=True
    )
    
    print("Ready! Speak into your microphone. Press Ctrl+C to stop.")
    
    try:
        while True:
            text = recorder.text()
            if text.strip():
                print(f"Transcribed: {text}")
    except KeyboardInterrupt:
        print("\nStopping...")
    finally:
        recorder.shutdown()

if __name__ == '__main__':
    main()
```

Run the script:
```bash
python test_ollama.py
```

### Method 2: Using Example Scripts

The repository includes several example scripts:

```bash
# Run the Ollama integration example
python examples/ollama_integration_example.py

# Run the local Ollama example
python tests/ollama_local_example.py

# Run a simple test
python tests/simple_test.py
```

### Method 3: Server Mode

Start the RealtimeSTT server with Ollama:

```bash
# Navigate to the server directory
cd RealtimeSTT_server

# Start server with Ollama integration
python stt_server.py --use_ollama --ollama_model gemma3:1b

# In another terminal, connect a client
python stt_cli_client.py
```

## Verification

### Step 1: Check Ollama Installation

Run the provided check script:

#### Windows
```bash
check_ollama.bat
```

#### macOS/Linux
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# List available models
ollama list

# Test model functionality
echo "test" | ollama run gemma3:1b
```

### Step 2: Verify RealtimeSTT Installation

```python
# Test import
from RealtimeSTT import AudioToTextRecorder
print("✓ RealtimeSTT imported successfully")

# Test Ollama integration
recorder = AudioToTextRecorder(use_ollama=True, use_microphone=False)
if hasattr(recorder, 'is_ollama_available'):
    print(f"✓ Ollama available: {recorder.is_ollama_available()}")
recorder.shutdown()
```

### Step 3: Test Audio Input

```python
# Test microphone access
import pyaudio

p = pyaudio.PyAudio()
print(f"✓ Audio devices found: {p.get_device_count()}")
p.terminate()
```

### Step 4: Full Integration Test

Run the complete test:

```bash
python -c "
from RealtimeSTT import AudioToTextRecorder
recorder = AudioToTextRecorder(
    use_ollama=True,
    ollama_model='gemma3:1b',
    use_microphone=False,
    spinner=False
)
print('✓ Full integration test passed')
recorder.shutdown()
"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "Ollama server not accessible"

**Problem**: Cannot connect to Ollama server

**Solutions**:
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not running, start it
ollama serve

# Check if port 11434 is available
netstat -an | grep 11434  # Linux/macOS
netstat -an | findstr 11434  # Windows
```

#### 2. "Model not found"

**Problem**: Specified model is not available

**Solutions**:
```bash
# List available models
ollama list

# Pull the required model
ollama pull gemma3:1b

# Verify model is downloaded
ollama list | grep gemma3
```

#### 3. "PyAudio installation failed"

**Problem**: Cannot install PyAudio dependency

**Solutions**:

**Windows**:
```bash
# Install Microsoft C++ Build Tools first
# Then try: pip install pyaudio
# Or use pre-compiled wheel:
pip install pipwin
pipwin install pyaudio
```

**macOS**:
```bash
# Install PortAudio first
brew install portaudio
pip install pyaudio
```

**Linux**:
```bash
sudo apt-get install python3-dev portaudio19-dev
pip install pyaudio
```

#### 4. "CUDA/GPU issues"

**Problem**: GPU acceleration not working

**Solutions**:
```bash
# Check CUDA installation
nvidia-smi

# Reinstall PyTorch with CUDA support
pip uninstall torch torchaudio
pip install torch==2.5.1+cu121 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu121

# Test CUDA availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

#### 5. "Permission denied" (microphone access)

**Problem**: Cannot access microphone

**Solutions**:

**Windows**: Check Privacy Settings → Microphone → Allow apps to access microphone

**macOS**: System Preferences → Security & Privacy → Privacy → Microphone → Allow Terminal/Python

**Linux**: 
```bash
# Add user to audio group
sudo usermod -a -G audio $USER
# Logout and login again
```

#### 6. "Slow transcription performance"

**Problem**: Transcription is too slow

**Solutions**:
- Use a smaller model: `ollama pull gemma3:1b` instead of larger models
- Enable GPU acceleration (see CUDA setup above)
- Adjust configuration:
```python
config = {
    'beam_size': 3,  # Reduce from default 5
    'batch_size': 8,  # Reduce from default 16
    'realtime_processing_pause': 0.1,  # Increase from 0.02
}
```

#### 7. "Import errors"

**Problem**: Cannot import RealtimeSTT modules

**Solutions**:
```bash
# Reinstall RealtimeSTT
pip uninstall RealtimeSTT
pip install RealtimeSTT

# Check Python path
python -c "import sys; print(sys.path)"

# Install in development mode if using source
pip install -e .
```

### Getting Help

If you encounter issues not covered here:

1. **Check the logs**: Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **Test individual components**:
```bash
# Test Ollama separately
ollama run gemma3:1b "Hello, how are you?"

# Test RealtimeSTT without Ollama
python tests/simple_test.py
```

3. **Community support**:
   - GitHub Issues: [RealtimeSTT Issues](https://github.com/KoljaB/RealtimeSTT/issues)
   - Ollama Documentation: [ollama.ai/docs](https://ollama.ai/docs)

### Performance Tips

1. **Model Selection**:
   - `gemma3:1b`: Best balance of speed and accuracy
   - `llama3.2:1b`: Faster but less accurate
   - `llama3.2:3b`: More accurate but slower

2. **Hardware Optimization**:
   - Use GPU acceleration when available
   - Ensure sufficient RAM (8GB+ recommended)
   - Use SSD storage for better model loading

3. **Configuration Tuning**:
   - Adjust `silero_sensitivity` for voice detection
   - Tune `post_speech_silence_duration` for responsiveness
   - Enable `early_transcription_on_silence` for faster results

## Next Steps

Once you have RealtimeSTT working with Ollama:

1. **Explore Examples**: Check the `examples/` and `tests/` directories for more advanced usage patterns
2. **Customize Configuration**: Experiment with different models and settings
3. **Build Applications**: Use RealtimeSTT as a component in your own voice-enabled applications
4. **Contribute**: Consider contributing improvements back to the project

Congratulations! You now have a fully functional local speech-to-text system running with RealtimeSTT and Ollama.
