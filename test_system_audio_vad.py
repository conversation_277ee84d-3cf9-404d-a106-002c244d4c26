"""
Test VAD (Voice Activity Detection) with system audio to diagnose transcription issues.
"""

import time
import logging
from RealtimeSTT import AudioToTextRecorder

# Set up logging to see VAD activity
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_system_audio_vad():
    """Test system audio with VAD debugging."""
    print("Testing system audio with VAD debugging...")
    print("This will show when speech is detected by the VAD system.")
    print("Play some speech audio (YouTube video, etc.) and watch for VAD activity.")
    print()
    
    # Create recorder with very sensitive VAD settings
    config = {
        'model': 'tiny.en',
        'use_microphone': False,
        'use_system_audio': True,
        'system_audio_channels': 1,
        'system_audio_device_index': None,
        
        # Very sensitive VAD settings for testing
        'silero_sensitivity': 0.1,  # Very sensitive (0.1 = detect almost anything)
        'webrtc_sensitivity': 1,    # Very sensitive
        'post_speech_silence_duration': 0.5,  # Shorter silence duration
        'min_length_of_recording': 0.3,       # Shorter minimum recording
        'min_gap_between_recordings': 0.2,    # Shorter gap between recordings
        
        # Enable extended logging to see VAD activity
        'use_extended_logging': True,
        'level': logging.INFO,
        'spinner': False,
        
        # Disable Ollama for faster testing
        'use_ollama': False,
    }
    
    try:
        print("Initializing recorder with sensitive VAD settings...")
        recorder = AudioToTextRecorder(**config)
        
        print("✓ Recorder initialized")
        print(f"Audio source: {recorder.get_current_audio_source()}")
        
        # Show available devices
        try:
            devices = recorder.get_available_system_audio_devices()
            print(f"Using system audio device: {devices[0]['name'] if devices else 'Default'}")
        except:
            print("Using default system audio device")
        
        print()
        print("Listening for speech activity...")
        print("Watch for VAD messages like 'Silero VAD detected speech' or 'WebRTC VAD detected speech'")
        print("Press Ctrl+C to stop")
        print("-" * 60)
        
        # Listen for a short time with timeout
        start_time = time.time()
        timeout = 30  # 30 seconds
        
        while time.time() - start_time < timeout:
            try:
                # Use a short timeout on text() to avoid blocking forever
                text = recorder.text()
                if text and text.strip():
                    print(f"🎯 TRANSCRIBED: {text}")
                    print("-" * 60)
                    
            except KeyboardInterrupt:
                print("\n🛑 Stopping...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                continue
        
        if time.time() - start_time >= timeout:
            print(f"\n⏰ Test completed after {timeout} seconds")
            print("If no VAD activity was shown, the issue might be:")
            print("1. Audio levels too low for VAD detection")
            print("2. VAD models not detecting the audio as speech")
            print("3. Audio format/sample rate mismatch")
        
        recorder.shutdown()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_system_audio_vad()
