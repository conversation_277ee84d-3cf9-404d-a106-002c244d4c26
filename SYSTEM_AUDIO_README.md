# System Audio Capture for RealtimeSTT

This extension adds Windows WASAPI loopback audio capture functionality to RealtimeSTT, allowing you to transcribe system audio output (what you hear through speakers/headphones) in real-time.

## Features

- **System Audio Capture**: Record audio from Windows system output using WASAPI loopback mode
- **Real-time Transcription**: Transcribe system audio in real-time with the same quality as microphone input
- **Multiple Audio Sources**: Capture from video calls (Teams, Zoom), streaming media, system sounds, etc.
- **Audio Source Switching**: Toggle between microphone input and system audio capture
- **Device Selection**: Choose specific system audio devices for capture
- **Background Operation**: Works when application is minimized or hidden
- **Terminal Output**: All transcriptions are output to the terminal where the script was run

## Requirements

### System Requirements
- Windows OS with WASAPI support (Windows Vista and later)
- Python 3.7 or higher

### Dependencies
- `PyAudioWPatch>=********` - PyAudio fork with WASAPI loopback support
- `keyboard>=0.13.5` - For demo application controls
- All existing RealtimeSTT dependencies

## Installation

1. Install PyAudioWPatch for WASAPI loopback support:
```bash
pip install PyAudioWPatch
```

2. Install keyboard library for demo controls:
```bash
pip install keyboard
```

3. Or install all dependencies at once:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

```python
from RealtimeSTT import AudioToTextRecorder

# Initialize with system audio capture
recorder = AudioToTextRecorder(
    model='base',
    use_microphone=False,      # Disable microphone
    use_system_audio=True,     # Enable system audio capture
    system_audio_channels=1,   # Mono capture
    enable_realtime_transcription=True,
    on_realtime_transcription_update=lambda text: print(f"Live: {text}")
)

# Start transcribing system audio
while True:
    text = recorder.text()
    if text:
        print(f"Final: {text}")
```

### Audio Source Control

```python
# Switch between audio sources
recorder.set_microphone(True)           # Use microphone
recorder.set_system_audio(True)         # Use system audio
recorder.toggle_audio_source()          # Toggle between sources

# Check current source
current = recorder.get_current_audio_source()  # Returns: "microphone", "system_audio", or "none"

# List available system audio devices
devices = recorder.get_available_system_audio_devices()
for device in devices:
    print(f"{device['name']} - Index: {device['index']}")
```

### Configuration Options

```python
recorder = AudioToTextRecorder(
    # System audio parameters
    use_system_audio=True,                    # Enable system audio capture
    system_audio_device_index=None,           # Auto-select default device
    system_audio_sample_rate=16000,           # Sample rate (None for device default)
    system_audio_channels=1,                  # 1=mono, 2=stereo
    
    # Standard RealtimeSTT parameters
    model='base',
    language='en',
    enable_realtime_transcription=True,
    # ... other parameters
)
```

## Demo Applications

### System Audio Transcription Demo

Run the interactive demo application:

```bash
python system_audio_transcription_demo.py
```

**Controls:**
- `M` - Switch to microphone input
- `S` - Switch to system audio capture  
- `T` - Toggle between sources
- `L` - List available system audio devices
- `Q` - Quit application

### Test Suite

Validate the system audio capture functionality:

```bash
python test_system_audio.py
```

This will run comprehensive tests to ensure everything is working correctly.

## Use Cases

### Video Conferencing
Transcribe video calls from Teams, Zoom, Skype, etc.:
```python
# Start system audio capture before joining a call
recorder.set_system_audio(True)
# All call audio will be transcribed in real-time
```

### Media Transcription
Transcribe podcasts, videos, music, or any audio playing on your computer:
```python
# Play media in any application
# System audio capture will transcribe the audio content
```

### System Sound Monitoring
Monitor and transcribe system notifications, alerts, and other audio:
```python
# Capture all system audio including notifications
recorder = AudioToTextRecorder(
    use_system_audio=True,
    silero_sensitivity=0.1,  # Lower threshold for quiet sounds
)
```

## Technical Details

### WASAPI Loopback Mode
- Uses Windows Audio Session API (WASAPI) loopback functionality
- Captures the same audio that goes to your speakers/headphones
- Works with any audio application or source
- No additional drivers or virtual cables required

### Audio Processing
- System audio is processed through the same pipeline as microphone input
- Automatic sample rate conversion and format handling
- Real-time voice activity detection works with system audio
- All RealtimeSTT features (wake words, callbacks, etc.) are supported

### Device Selection
- Automatically detects available WASAPI loopback devices
- Can target specific audio devices (speakers, headphones, etc.)
- Fallback to default system audio device if none specified

## Troubleshooting

### PyAudioWPatch Not Available
If you see "PyAudioWPatch not available":
```bash
pip install PyAudioWPatch
```

### No System Audio Devices Found
- Ensure you're running on Windows with WASAPI support
- Check that audio devices are properly configured in Windows
- Try running as administrator if device access is restricted

### No Audio Captured
- Verify that audio is actually playing through your speakers/headphones
- Check Windows audio settings and ensure the correct playback device is selected
- Test with different audio sources (music, videos, etc.)

### Permission Issues
- Some systems may require administrator privileges for WASAPI loopback
- Try running your Python script as administrator

## Limitations

- **Windows Only**: WASAPI loopback is a Windows-specific feature
- **Output Audio Only**: Captures what goes to speakers, not microphone input
- **System Dependent**: Requires WASAPI-compatible audio drivers
- **No Mixing**: Cannot simultaneously capture microphone and system audio (use toggle)

## Examples

See the included demo applications:
- `system_audio_transcription_demo.py` - Interactive demo with controls
- `test_system_audio.py` - Comprehensive test suite
- `tests/realtimestt_test_stereomix.py` - Original stereo mix example

## Contributing

When contributing to system audio functionality:
1. Test on multiple Windows versions
2. Ensure compatibility with various audio devices
3. Handle edge cases gracefully (no devices, permission issues, etc.)
4. Update documentation and examples

## License

This system audio capture extension follows the same license as RealtimeSTT.
