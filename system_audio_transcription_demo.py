#!/usr/bin/env python3
"""
System Audio Transcription Demo for RealtimeSTT

This script demonstrates real-time transcription of system audio output
(what you hear through speakers/headphones) using Windows WASAPI loopback mode.

Features:
- Capture audio from system output (speakers, headphones)
- Real-time transcription of video calls, media playback, system sounds
- Toggle between microphone and system audio capture
- Works when application is minimized or hidden
- Terminal-based output with controls

Requirements:
- Windows OS with WASAPI support
- PyAudioWPatch library for loopback capture
- RealtimeSTT with system audio support

Controls:
- Press 'M' to switch to microphone input
- Press 'S' to switch to system audio capture
- Press 'T' to toggle between sources
- Press 'L' to list available system audio devices
- Press 'Q' to quit

Author: RealtimeSTT System Audio Extension
"""

import sys
import os
import time
import threading
import keyboard
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    print("Please install: pip install RealtimeSTT")
    sys.exit(1)

# Check for PyAudioWPatch
try:
    import pyaudiowpatch
    print("✓ PyAudioWPatch available - System audio capture supported")
    SYSTEM_AUDIO_AVAILABLE = True
except ImportError:
    print("⚠ PyAudioWPatch not available - System audio capture disabled")
    print("Install with: pip install PyAudioWPatch")
    SYSTEM_AUDIO_AVAILABLE = False

class SystemAudioTranscriptionDemo:
    """
    Demo application for system audio transcription.
    """
    
    def __init__(self):
        self.recorder = None
        self.is_running = False
        self.current_source = "none"
        self.transcription_count = 0
        
        # Console formatting
        self.RESET = '\033[0m'
        self.BOLD = '\033[1m'
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BLUE = '\033[94m'
        self.CYAN = '\033[96m'
        
    def print_header(self):
        """Print the application header."""
        print("\n" + "=" * 70)
        print(f"{self.BOLD}{self.CYAN}System Audio Transcription Demo{self.RESET}")
        print("=" * 70)
        print("Real-time transcription of system audio output")
        print("Capture audio from speakers, video calls, media playback, etc.")
        print()
        
        if SYSTEM_AUDIO_AVAILABLE:
            print(f"{self.GREEN}✓ System audio capture available{self.RESET}")
        else:
            print(f"{self.RED}✗ System audio capture not available{self.RESET}")
            print("Install PyAudioWPatch for system audio support")
        print()
    
    def print_controls(self):
        """Print the control instructions."""
        print(f"{self.BOLD}Controls:{self.RESET}")
        print("  M - Switch to microphone input")
        if SYSTEM_AUDIO_AVAILABLE:
            print("  S - Switch to system audio capture")
            print("  T - Toggle between microphone and system audio")
            print("  L - List available system audio devices")
        print("  Q - Quit application")
        print("-" * 70)
        print()
    
    def on_realtime_update(self, text):
        """Callback for real-time transcription updates."""
        if text.strip():
            # Clear the line and print the current transcription
            print(f"\r{self.CYAN}🎤 Live [{self.current_source}]: {text}{self.RESET}", end="", flush=True)
    
    def on_final_transcription(self, text):
        """Callback for final transcription result."""
        if text.strip():
            self.transcription_count += 1
            timestamp = time.strftime("%H:%M:%S")
            print(f"\n{self.GREEN}[{timestamp}] Final [{self.current_source}]: {text}{self.RESET}")
            print(f"{self.YELLOW}Transcriptions: {self.transcription_count}{self.RESET}")
            print("-" * 50)
    
    def on_recording_start(self):
        """Callback when recording starts."""
        print(f"{self.BLUE}🔴 Recording started ({self.current_source}){self.RESET}")
    
    def on_recording_stop(self):
        """Callback when recording stops."""
        print(f"\n{self.BLUE}⏹️  Recording stopped, processing...{self.RESET}")
    
    def list_system_audio_devices(self):
        """List available system audio devices."""
        if not SYSTEM_AUDIO_AVAILABLE or not self.recorder:
            print(f"{self.RED}System audio devices not available{self.RESET}")
            return
        
        print(f"\n{self.BOLD}Available System Audio Devices:{self.RESET}")
        try:
            devices = self.recorder.get_available_system_audio_devices()
            if devices:
                for i, device in enumerate(devices):
                    print(f"  {i}: {device['name']} (Index: {device['index']})")
                    print(f"     Channels: {device['maxInputChannels']}, "
                          f"Sample Rate: {device['defaultSampleRate']}")
            else:
                print("  No system audio devices found")
        except Exception as e:
            print(f"{self.RED}Error listing devices: {e}{self.RESET}")
        print()
    
    def switch_to_microphone(self):
        """Switch to microphone input."""
        if self.recorder:
            self.recorder.set_microphone(True)
            self.recorder.set_system_audio(False)
            self.current_source = "microphone"
            print(f"{self.GREEN}Switched to microphone input{self.RESET}")
    
    def switch_to_system_audio(self):
        """Switch to system audio capture."""
        if not SYSTEM_AUDIO_AVAILABLE:
            print(f"{self.RED}System audio capture not available{self.RESET}")
            return
        
        if self.recorder:
            self.recorder.set_microphone(False)
            self.recorder.set_system_audio(True)
            self.current_source = "system_audio"
            print(f"{self.GREEN}Switched to system audio capture{self.RESET}")
    
    def toggle_audio_source(self):
        """Toggle between microphone and system audio."""
        if not SYSTEM_AUDIO_AVAILABLE:
            print(f"{self.RED}System audio capture not available{self.RESET}")
            return
        
        if self.recorder:
            self.recorder.toggle_audio_source()
            self.current_source = self.recorder.get_current_audio_source()
            print(f"{self.GREEN}Toggled to {self.current_source}{self.RESET}")
    
    def handle_keyboard_input(self):
        """Handle keyboard input for controls."""
        while self.is_running:
            try:
                if keyboard.is_pressed('q'):
                    print(f"\n{self.YELLOW}Quit requested...{self.RESET}")
                    self.is_running = False
                    break
                elif keyboard.is_pressed('m'):
                    self.switch_to_microphone()
                    time.sleep(0.5)  # Prevent multiple triggers
                elif keyboard.is_pressed('s') and SYSTEM_AUDIO_AVAILABLE:
                    self.switch_to_system_audio()
                    time.sleep(0.5)
                elif keyboard.is_pressed('t') and SYSTEM_AUDIO_AVAILABLE:
                    self.toggle_audio_source()
                    time.sleep(0.5)
                elif keyboard.is_pressed('l') and SYSTEM_AUDIO_AVAILABLE:
                    self.list_system_audio_devices()
                    time.sleep(0.5)
                
                time.sleep(0.1)  # Small delay to prevent high CPU usage
                
            except Exception as e:
                # Handle keyboard library errors gracefully
                time.sleep(0.1)
    
    def initialize_recorder(self):
        """Initialize the audio recorder."""
        print("Initializing RealtimeSTT...")
        
        # Configuration for real-time transcription
        config = {
            # Core settings
            'model': 'base',
            'language': 'en',
            'use_microphone': True,  # Start with microphone
            'spinner': False,
            
            # System audio settings
            'use_system_audio': False,  # Will be enabled when switched
            'system_audio_channels': 1,
            
            # Real-time transcription
            'enable_realtime_transcription': True,
            'realtime_model_type': 'tiny.en',
            'realtime_processing_pause': 0.1,
            'use_main_model_for_realtime': False,
            
            # Voice activity detection
            'silero_sensitivity': 0.4,
            'webrtc_sensitivity': 3,
            'post_speech_silence_duration': 1.0,
            'min_length_of_recording': 0.5,
            'min_gap_between_recordings': 0.5,
            
            # Performance settings
            'beam_size': 5,
            'beam_size_realtime': 3,
            'batch_size': 16,
            'realtime_batch_size': 8,
            
            # Callbacks
            'on_realtime_transcription_update': self.on_realtime_update,
            'on_recording_start': self.on_recording_start,
            'on_recording_stop': self.on_recording_stop,
            
            # Logging
            'level': 30,  # WARNING level to reduce noise
        }
        
        try:
            self.recorder = AudioToTextRecorder(**config)
            self.current_source = self.recorder.get_current_audio_source()
            print(f"{self.GREEN}✓ RealtimeSTT initialized successfully{self.RESET}")
            print(f"Current audio source: {self.current_source}")
            return True
            
        except Exception as e:
            print(f"{self.RED}✗ Failed to initialize recorder: {e}{self.RESET}")
            return False
    
    def run(self):
        """Run the demo application."""
        self.print_header()
        self.print_controls()
        
        if not self.initialize_recorder():
            return 1
        
        self.is_running = True
        
        # Start keyboard input handler in a separate thread
        keyboard_thread = threading.Thread(target=self.handle_keyboard_input, daemon=True)
        keyboard_thread.start()
        
        print(f"{self.BOLD}{self.GREEN}🎤 READY FOR TRANSCRIPTION!{self.RESET}")
        print(f"Current source: {self.current_source}")
        print("Start speaking or play audio...")
        print("Use keyboard controls to switch audio sources")
        print("-" * 70)
        print()
        
        try:
            # Main transcription loop
            while self.is_running:
                try:
                    # Get final transcription
                    final_text = self.recorder.text()
                    
                    if final_text.strip():
                        self.on_final_transcription(final_text)
                        
                except KeyboardInterrupt:
                    print(f"\n{self.YELLOW}Keyboard interrupt received{self.RESET}")
                    break
                except Exception as e:
                    print(f"\n{self.RED}Error during transcription: {e}{self.RESET}")
                    continue
        
        except Exception as e:
            print(f"{self.RED}Fatal error: {e}{self.RESET}")
            return 1
        
        finally:
            # Cleanup
            self.is_running = False
            if self.recorder:
                try:
                    print(f"\n{self.YELLOW}Shutting down recorder...{self.RESET}")
                    self.recorder.shutdown()
                    print(f"{self.GREEN}✓ Recorder shutdown complete{self.RESET}")
                except:
                    pass
        
        print(f"\n{self.CYAN}🎉 System audio transcription demo completed!{self.RESET}")
        print(f"Total transcriptions: {self.transcription_count}")
        return 0

def main():
    """Main entry point."""
    demo = SystemAudioTranscriptionDemo()
    return demo.run()

if __name__ == "__main__":
    sys.exit(main())
