@echo off
REM Ollama Installation Check Script for RealtimeSTT
REM This script checks if Ollama is properly installed and configured

echo ============================================================================
echo RealtimeSTT Ollama Installation Check
echo ============================================================================
echo.

REM Check if Ollama is installed
echo [1/4] Checking Ollama installation...
ollama --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama is installed
    for /f "tokens=*" %%i in ('ollama --version') do echo   Version: %%i
) else (
    echo ✗ Ollama is not installed
    echo.
    echo Please install Ollama:
    echo 1. Visit https://ollama.ai
    echo 2. Download and install Ollama for Windows
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo.

REM Check if Ollama server is running
echo [2/4] Checking Ollama server status...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Ollama server is running
) else (
    echo ✗ Ollama server is not running
    echo.
    echo Starting Ollama server...
    start /b ollama serve
    echo Waiting for server to start...
    timeout /t 5 /nobreak >nul
    
    REM Check again
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Ollama server started successfully
    ) else (
        echo ✗ Failed to start Ollama server
        echo Please start Ollama manually: ollama serve
        echo.
        pause
        exit /b 1
    )
)

echo.

REM Check for gemma3:1b model
echo [3/4] Checking for gemma3:1b model...
ollama list | findstr "gemma3:1b" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ gemma3:1b model is available
) else (
    echo ✗ gemma3:1b model not found
    echo.
    echo Downloading gemma3:1b model (this may take a while)...
    ollama pull gemma3:1b
    if %errorlevel% equ 0 (
        echo ✓ gemma3:1b model downloaded successfully
    ) else (
        echo ✗ Failed to download gemma3:1b model
        echo Please try manually: ollama pull gemma3:1b
        echo.
        pause
        exit /b 1
    )
)

echo.

REM Test model functionality
echo [4/4] Testing model functionality...
echo test | ollama run gemma3:1b --verbose >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Model is working correctly
) else (
    echo ⚠ Model test failed, but this might be normal
    echo The model should still work with RealtimeSTT
)

echo.
echo ============================================================================
echo Ollama Setup Complete!
echo ============================================================================
echo.
echo Your RealtimeSTT is now configured for local-first AI processing.
echo.
echo Available models:
ollama list
echo.
echo Configuration:
echo   Ollama Host: localhost:11434
echo   Default Model: gemma3:1b
echo   Fallback: Whisper models (if Ollama unavailable)
echo.
echo You can now run RealtimeSTT applications with local AI support!
echo.
pause
