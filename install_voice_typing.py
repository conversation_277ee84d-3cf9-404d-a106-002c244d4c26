#!/usr/bin/env python3
"""
Installation script for Universal Voice Typing

This script installs all required dependencies for the voice typing system.
"""

import subprocess
import sys
import platform

def install_package(package):
    """Install a Python package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🔧 Installing Universal Voice Typing Dependencies")
    print("=" * 50)
    
    # Required packages
    packages = [
        "RealtimeSTT",
        "pyautogui", 
        "pynput",
        "requests",
        "faster-whisper",
        "torch",
        "torchaudio"
    ]
    
    # Platform-specific packages
    system = platform.system().lower()
    if system == "linux":
        print("📋 Linux detected - you may need to install system dependencies:")
        print("sudo apt-get update")
        print("sudo apt-get install python3-dev portaudio19-dev")
        print()
    elif system == "darwin":  # macOS
        print("📋 macOS detected - you may need to install PortAudio:")
        print("brew install portaudio")
        print()
    
    print("Installing Python packages...")
    
    failed_packages = []
    for package in packages:
        print(f"Installing {package}...", end=" ")
        if install_package(package):
            print("✅")
        else:
            print("❌")
            failed_packages.append(package)
    
    print()
    
    if failed_packages:
        print("❌ Some packages failed to install:")
        for package in failed_packages:
            print(f"  - {package}")
        print()
        print("Try installing them manually:")
        for package in failed_packages:
            print(f"pip install {package}")
    else:
        print("✅ All packages installed successfully!")
    
    print()
    print("🎤 Next steps:")
    print("1. Start Ollama: ollama serve")
    print("2. Download model: ollama pull gemma3:1b")
    print("3. Run voice typing: python simple_voice_typing.py")

if __name__ == "__main__":
    main()
