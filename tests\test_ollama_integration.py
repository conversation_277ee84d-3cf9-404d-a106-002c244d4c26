#!/usr/bin/env python3
"""
Test script for RealtimeSTT Ollama integration

This script tests the Ollama integration functionality to ensure
all implemented features work correctly.
"""

import sys
import os
import unittest
from pathlib import Path

# Add the parent directory to the path to import RealtimeSTT
sys.path.append(str(Path(__file__).parent.parent))

try:
    from RealtimeSTT import AudioToTextRecorder
    from RealtimeSTT.ollama_client import OllamaClient
    from RealtimeSTT.config_migration import ConfigMigration
except ImportError as e:
    print(f"Failed to import RealtimeSTT modules: {e}")
    sys.exit(1)


class TestOllamaIntegration(unittest.TestCase):
    """Test cases for Ollama integration functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            'model': 'gemma3:1b',
            'use_ollama': True,
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'ollama_timeout': 30.0,
            'ollama_max_retries': 3,
            'ollama_auto_download': True,
            'fallback_to_external': True,
            'use_microphone': False,  # Don't use microphone in tests
            'spinner': False,
        }
    
    def test_ollama_client_initialization(self):
        """Test OllamaClient can be initialized."""
        try:
            client = OllamaClient(
                host='localhost:11434',
                model='gemma3:1b',
                timeout=30.0,
                max_retries=3
            )
            self.assertIsNotNone(client)
            self.assertEqual(client.model, 'gemma3:1b')
            self.assertEqual(client.host, 'localhost:11434')
            print("✓ OllamaClient initialization test passed")
        except Exception as e:
            print(f"⚠ OllamaClient initialization test failed: {e}")
            self.skipTest("OllamaClient initialization failed")
    
    def test_config_migration(self):
        """Test configuration migration functionality."""
        try:
            migration = ConfigMigration()
            
            # Test with old-style config
            old_config = {
                'model': 'medium',
                'language': 'en',
                'use_microphone': False
            }
            
            migrated_config = migration.migrate_recorder_config(old_config)
            
            # Check that Ollama parameters were added
            self.assertIn('use_ollama', migrated_config)
            self.assertIn('ollama_model', migrated_config)
            self.assertIn('ollama_host', migrated_config)
            self.assertIn('fallback_to_external', migrated_config)
            
            # Check that original parameters are preserved
            self.assertEqual(migrated_config['language'], 'en')
            self.assertEqual(migrated_config['use_microphone'], False)
            
            print("✓ Configuration migration test passed")
        except Exception as e:
            print(f"✗ Configuration migration test failed: {e}")
            self.fail(f"Configuration migration failed: {e}")
    
    def test_recorder_initialization_with_ollama(self):
        """Test AudioToTextRecorder initialization with Ollama config."""
        try:
            recorder = AudioToTextRecorder(**self.test_config)
            self.assertIsNotNone(recorder)
            
            # Check Ollama-related attributes
            self.assertTrue(hasattr(recorder, 'use_ollama'))
            self.assertTrue(hasattr(recorder, 'ollama_model'))
            self.assertTrue(hasattr(recorder, 'ollama_host'))
            self.assertTrue(hasattr(recorder, 'ollama_client'))
            
            # Check Ollama methods are available
            self.assertTrue(hasattr(recorder, 'switch_to_ollama_model'))
            self.assertTrue(hasattr(recorder, 'switch_to_whisper_model'))
            self.assertTrue(hasattr(recorder, 'get_available_ollama_models'))
            self.assertTrue(hasattr(recorder, 'is_ollama_available'))
            
            print("✓ Recorder initialization with Ollama test passed")
            
            # Cleanup
            recorder.shutdown()
            
        except Exception as e:
            print(f"⚠ Recorder initialization test failed: {e}")
            print("This is expected if Ollama is not running")
    
    def test_fallback_configuration(self):
        """Test fallback configuration when Ollama is unavailable."""
        try:
            # Create config that should fallback gracefully
            fallback_config = self.test_config.copy()
            fallback_config['ollama_host'] = 'invalid:9999'  # Invalid host
            
            recorder = AudioToTextRecorder(**fallback_config)
            self.assertIsNotNone(recorder)
            
            print("✓ Fallback configuration test passed")
            
            # Cleanup
            recorder.shutdown()
            
        except Exception as e:
            print(f"✗ Fallback configuration test failed: {e}")
            self.fail(f"Fallback configuration failed: {e}")
    
    def test_model_switching_methods(self):
        """Test model switching method availability."""
        try:
            recorder = AudioToTextRecorder(**self.test_config)
            
            # Test method existence
            self.assertTrue(callable(getattr(recorder, 'switch_to_ollama_model', None)))
            self.assertTrue(callable(getattr(recorder, 'switch_to_whisper_model', None)))
            self.assertTrue(callable(getattr(recorder, 'get_available_ollama_models', None)))
            self.assertTrue(callable(getattr(recorder, 'is_ollama_available', None)))
            
            # Test method calls (they should not raise exceptions)
            models = recorder.get_available_ollama_models()
            self.assertIsInstance(models, list)
            
            available = recorder.is_ollama_available()
            self.assertIsInstance(available, bool)
            
            print("✓ Model switching methods test passed")
            
            # Cleanup
            recorder.shutdown()
            
        except Exception as e:
            print(f"⚠ Model switching methods test failed: {e}")
            print("This is expected if Ollama is not running")


def run_integration_test():
    """Run the integration test suite."""
    print("=" * 60)
    print("RealtimeSTT Ollama Integration Test Suite")
    print("=" * 60)
    print()
    
    # Check if Ollama is available
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama server is running")
            ollama_available = True
        else:
            print("⚠ Ollama server not responding properly")
            ollama_available = False
    except Exception:
        print("⚠ Ollama server not accessible")
        ollama_available = False
    
    if not ollama_available:
        print()
        print("Note: Some tests may be skipped because Ollama is not running.")
        print("To run full tests:")
        print("1. Install Ollama from https://ollama.ai")
        print("2. Start Ollama: 'ollama serve'")
        print("3. Pull model: 'ollama pull gemma3:1b'")
        print()
    
    # Run the test suite
    unittest.main(argv=[''], exit=False, verbosity=2)


if __name__ == "__main__":
    run_integration_test()
