#!/usr/bin/env python3
"""
Debug version of wake word detection
"""

import sys
import os
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

print("Starting debug script...")

try:
    print("Importing RealtimeSTT...")
    from RealtimeSTT import AudioToTextRecorder
    print("✓ RealtimeSTT imported successfully")
except ImportError as e:
    print(f"✗ Failed to import RealtimeSTT: {e}")
    sys.exit(1)

def check_ollama():
    """Check Ollama status."""
    try:
        print("Checking Ollama...")
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [m['name'] for m in models]
            
            if any('gemma3:1b' in name for name in model_names):
                print("✓ Ollama gemma3:1b model available")
                return True
            else:
                print("✗ gemma3:1b model not found")
                return False
        else:
            print(f"✗ Ollama server error: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Ollama not accessible: {e}")
        return False

def test_simple_recorder():
    """Test creating a simple recorder."""
    try:
        print("Creating simple recorder...")
        config = {
            'model': 'tiny.en',  # Required by RealtimeSTT
            'use_ollama': True,
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'ollama_timeout': 30.0,
            'language': 'en',
            'use_microphone': True,
            'use_system_audio': False,
            'spinner': False,
            'silero_sensitivity': 0.4,
            'webrtc_sensitivity': 3,
            'post_speech_silence_duration': 1.0,
            'min_length_of_recording': 0.5,
            'min_gap_between_recordings': 0.5,
        }
        
        print("Initializing AudioToTextRecorder...")
        recorder = AudioToTextRecorder(**config)
        print("✓ Simple recorder created successfully")
        
        print("Testing one transcription...")
        print("Say something...")
        
        text = recorder.text()
        print(f"Transcribed: '{text}'")
        
        recorder.shutdown()
        print("✓ Recorder shutdown complete")
        return True
        
    except Exception as e:
        print(f"❌ Error creating simple recorder: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("Debug Wake Word Detection")
    print("=" * 50)
    
    # Check Ollama
    ollama_ok = check_ollama()
    print()
    
    # Test simple recorder
    print("Testing simple recorder...")
    if test_simple_recorder():
        print("✓ Basic functionality works")
    else:
        print("❌ Basic functionality failed")
        return 1
    
    print("\nDebug complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
