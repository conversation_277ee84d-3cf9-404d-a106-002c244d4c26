#!/usr/bin/env python3
"""
Simple System Audio Transcription Demo

A basic demonstration of system audio capture and transcription
without complex keyboard controls.
"""

import sys
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

def main():
    print("=" * 60)
    print("Simple System Audio Transcription Demo")
    print("=" * 60)
    print("This demo will capture and transcribe system audio for 30 seconds")
    print("Play some audio (music, video, etc.) to see transcription")
    print()
    
    try:
        from RealtimeSTT import AudioToTextRecorder
        
        # Check for PyAudioWPatch
        try:
            import pyaudiowpatch
            print("✓ PyAudioWPatch available - System audio capture supported")
        except ImportError:
            print("⚠ PyAudioWPatch not available - Install with: pip install PyAudioWPatch")
            return 1
        
        print("Initializing system audio transcription...")
        
        # Simple callback for transcription updates
        def on_transcription(text):
            if text.strip():
                timestamp = time.strftime("%H:%M:%S")
                print(f"[{timestamp}] Transcribed: {text}")
        
        # Initialize recorder with system audio
        recorder = AudioToTextRecorder(
            model='tiny.en',
            use_microphone=False,
            use_system_audio=True,
            system_audio_channels=1,
            enable_realtime_transcription=True,
            realtime_model_type='tiny.en',
            realtime_processing_pause=0.2,
            on_realtime_transcription_update=lambda text: print(f"Live: {text}", end='\r') if text.strip() else None,
            spinner=False,
            level=30,  # WARNING level
        )
        
        print(f"✓ Recorder initialized")
        print(f"Current audio source: {recorder.get_current_audio_source()}")
        
        # List available devices
        devices = recorder.get_available_system_audio_devices()
        print(f"Available system audio devices: {len(devices)}")
        for i, device in enumerate(devices[:3]):  # Show first 3 devices
            print(f"  {i}: {device['name']}")
        
        print("\n" + "=" * 60)
        print("🎤 LISTENING FOR SYSTEM AUDIO")
        print("=" * 60)
        print("Play some audio to see transcription...")
        print("This demo will run for 30 seconds")
        print("-" * 60)
        
        # Run for 30 seconds
        start_time = time.time()
        transcription_count = 0
        
        while time.time() - start_time < 30:
            try:
                # Get transcription (this will block until audio is detected)
                text = recorder.text()
                if text.strip():
                    transcription_count += 1
                    on_transcription(text)
                    print()  # New line after transcription
                    
            except KeyboardInterrupt:
                print("\nStopping demo...")
                break
            except Exception as e:
                print(f"Error during transcription: {e}")
                continue
        
        print("\n" + "=" * 60)
        print("Demo completed!")
        print(f"Total transcriptions: {transcription_count}")
        print("=" * 60)
        
        # Cleanup
        recorder.shutdown()
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
