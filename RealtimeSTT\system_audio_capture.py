"""
System Audio Capture Module for RealtimeSTT

This module provides Windows WASAPI loopback audio capture functionality
to record system audio output (what you hear through speakers/headphones).
"""

import logging
import threading
import time
import numpy as np
from typing import Optional, Callable, Dict, Any, List
import queue

logger = logging.getLogger(__name__)

# Try to import PyAudioWPatch for WASAPI loopback support
try:
    import pyaudiowpatch as pyaudio_wpatch
    PYAUDIO_WPATCH_AVAILABLE = True
except ImportError:
    pyaudio_wpatch = None
    PYAUDIO_WPATCH_AVAILABLE = False

# Fallback to regular PyAudio
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    pyaudio = None
    PYAUDIO_AVAILABLE = False


class SystemAudioCapture:
    """
    Captures system audio output using Windows WASAPI loopback mode.
    
    This class provides functionality to capture audio from the system's
    audio output devices (speakers, headphones) rather than input devices
    (microphones). It supports real-time audio streaming and can capture
    audio from video calls, media playback, system sounds, etc.
    """
    
    def __init__(
        self,
        sample_rate: int = 16000,
        chunk_size: int = 1024,
        channels: int = 1,
        audio_format: int = None,
        device_index: Optional[int] = None,
        on_audio_data: Optional[Callable[[bytes], None]] = None,
        debug_mode: bool = False
    ):
        """
        Initialize the system audio capture.
        
        Args:
            sample_rate: Target sample rate for audio capture
            chunk_size: Number of frames per buffer
            channels: Number of audio channels (1 for mono, 2 for stereo)
            audio_format: PyAudio format (defaults to paInt16)
            device_index: Specific WASAPI loopback device index (None for default)
            on_audio_data: Callback function for audio data
            debug_mode: Enable debug logging
        """
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        self.audio_format = audio_format or (pyaudio_wpatch.paInt16 if PYAUDIO_WPATCH_AVAILABLE else pyaudio.paInt16)
        self.device_index = device_index
        self.on_audio_data = on_audio_data
        self.debug_mode = debug_mode
        
        # Audio interface and stream
        self.audio_interface = None
        self.stream = None
        self.is_recording = False
        self.recording_thread = None
        self.stop_event = threading.Event()
        
        # Device information
        self.device_info = None
        self.actual_sample_rate = sample_rate
        
        # Audio buffer for processing
        self.audio_queue = queue.Queue()
        
        # Check availability
        if not PYAUDIO_WPATCH_AVAILABLE and not PYAUDIO_AVAILABLE:
            raise ImportError("Neither PyAudioWPatch nor PyAudio is available. Please install one of them.")
        
        if not PYAUDIO_WPATCH_AVAILABLE:
            logger.warning("PyAudioWPatch not available. WASAPI loopback capture may not work properly.")
    
    def get_available_loopback_devices(self) -> List[Dict[str, Any]]:
        """
        Get a list of available WASAPI loopback devices.
        
        Returns:
            List of device information dictionaries
        """
        devices = []
        
        if not PYAUDIO_WPATCH_AVAILABLE:
            logger.warning("PyAudioWPatch not available. Cannot enumerate loopback devices.")
            return devices
        
        try:
            with pyaudio_wpatch.PyAudio() as p:
                # Get WASAPI host API info
                try:
                    wasapi_info = p.get_host_api_info_by_type(pyaudio_wpatch.paWASAPI)
                except OSError:
                    logger.error("WASAPI not available on this system")
                    return devices
                
                # Enumerate loopback devices
                for loopback in p.get_loopback_device_info_generator():
                    devices.append({
                        'index': loopback['index'],
                        'name': loopback['name'],
                        'maxInputChannels': loopback['maxInputChannels'],
                        'defaultSampleRate': loopback['defaultSampleRate'],
                        'hostApi': loopback['hostApi']
                    })
                    
                    if self.debug_mode:
                        logger.debug(f"Found loopback device: {loopback['name']} (index: {loopback['index']})")
        
        except Exception as e:
            logger.error(f"Error enumerating loopback devices: {e}")
        
        return devices
    
    def get_default_loopback_device(self) -> Optional[Dict[str, Any]]:
        """
        Get the default WASAPI loopback device (usually default speakers).
        
        Returns:
            Device information dictionary or None if not found
        """
        if not PYAUDIO_WPATCH_AVAILABLE:
            logger.warning("PyAudioWPatch not available. Cannot get default loopback device.")
            return None
        
        try:
            with pyaudio_wpatch.PyAudio() as p:
                # Get default WASAPI speakers
                try:
                    wasapi_info = p.get_host_api_info_by_type(pyaudio_wpatch.paWASAPI)
                    default_speakers = p.get_device_info_by_index(wasapi_info["defaultOutputDevice"])
                except OSError:
                    logger.error("WASAPI not available on this system")
                    return None
                
                # Check if it's already a loopback device
                if default_speakers.get("isLoopbackDevice", False):
                    return default_speakers
                
                # Find corresponding loopback device
                for loopback in p.get_loopback_device_info_generator():
                    if default_speakers["name"] in loopback["name"]:
                        if self.debug_mode:
                            logger.debug(f"Found default loopback device: {loopback['name']}")
                        return loopback
                
                logger.warning("Default loopback device not found")
                return None
                
        except Exception as e:
            logger.error(f"Error getting default loopback device: {e}")
            return None
    
    def setup_device(self) -> bool:
        """
        Setup the audio device for capture.
        
        Returns:
            True if setup successful, False otherwise
        """
        try:
            # Use PyAudioWPatch if available, otherwise fallback to PyAudio
            audio_module = pyaudio_wpatch if PYAUDIO_WPATCH_AVAILABLE else pyaudio
            self.audio_interface = audio_module.PyAudio()
            
            # Get device info
            if self.device_index is None:
                if PYAUDIO_WPATCH_AVAILABLE:
                    self.device_info = self.get_default_loopback_device()
                    if self.device_info is None:
                        logger.error("No default loopback device found")
                        return False
                    self.device_index = self.device_info['index']
                else:
                    # Fallback to default input device
                    self.device_index = self.audio_interface.get_default_input_device_info()['index']
                    self.device_info = self.audio_interface.get_device_info_by_index(self.device_index)
            else:
                self.device_info = self.audio_interface.get_device_info_by_index(self.device_index)
            
            # Update sample rate to match device capabilities
            self.actual_sample_rate = int(self.device_info.get('defaultSampleRate', self.sample_rate))
            
            if self.debug_mode:
                logger.debug(f"Using device: {self.device_info['name']} (index: {self.device_index})")
                logger.debug(f"Sample rate: {self.actual_sample_rate}, Channels: {self.channels}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up audio device: {e}")
            return False
    
    def start_capture(self) -> bool:
        """
        Start audio capture.
        
        Returns:
            True if capture started successfully, False otherwise
        """
        if self.is_recording:
            logger.warning("Audio capture is already running")
            return True
        
        if not self.setup_device():
            return False
        
        try:
            # Open audio stream
            self.stream = self.audio_interface.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.actual_sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size,
                input_device_index=self.device_index,
                stream_callback=self._audio_callback if self.on_audio_data else None
            )
            
            self.is_recording = True
            self.stop_event.clear()
            
            # Start recording thread if no callback is provided
            if not self.on_audio_data:
                self.recording_thread = threading.Thread(target=self._recording_loop, daemon=True)
                self.recording_thread.start()
            
            if self.debug_mode:
                logger.debug("System audio capture started")
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting audio capture: {e}")
            self.cleanup()
            return False
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """
        Audio stream callback function.
        
        Args:
            in_data: Input audio data
            frame_count: Number of frames
            time_info: Timing information
            status: Stream status
            
        Returns:
            Tuple of (data, continue_flag)
        """
        if self.on_audio_data and in_data:
            try:
                self.on_audio_data(in_data)
            except Exception as e:
                logger.error(f"Error in audio callback: {e}")
        
        return (in_data, pyaudio_wpatch.paContinue if PYAUDIO_WPATCH_AVAILABLE else pyaudio.paContinue)
    
    def _recording_loop(self):
        """
        Main recording loop for non-callback mode.
        """
        try:
            while self.is_recording and not self.stop_event.is_set():
                if self.stream and self.stream.is_active():
                    try:
                        data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                        if data and self.on_audio_data:
                            self.on_audio_data(data)
                        elif data:
                            self.audio_queue.put(data)
                    except Exception as e:
                        logger.error(f"Error reading audio data: {e}")
                        break
                else:
                    time.sleep(0.01)
                    
        except Exception as e:
            logger.error(f"Error in recording loop: {e}")
        finally:
            if self.debug_mode:
                logger.debug("Recording loop ended")
    
    def read_audio(self, timeout: float = 1.0) -> Optional[bytes]:
        """
        Read audio data from the queue (non-callback mode).
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            Audio data bytes or None if timeout
        """
        try:
            return self.audio_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def stop_capture(self):
        """
        Stop audio capture.
        """
        if not self.is_recording:
            return
        
        self.is_recording = False
        self.stop_event.set()
        
        # Wait for recording thread to finish
        if self.recording_thread and self.recording_thread.is_alive():
            self.recording_thread.join(timeout=2.0)
        
        self.cleanup()
        
        if self.debug_mode:
            logger.debug("System audio capture stopped")
    
    def cleanup(self):
        """
        Clean up audio resources.
        """
        try:
            if self.stream:
                if self.stream.is_active():
                    self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            if self.audio_interface:
                self.audio_interface.terminate()
                self.audio_interface = None
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def is_available(self) -> bool:
        """
        Check if system audio capture is available.
        
        Returns:
            True if available, False otherwise
        """
        return PYAUDIO_WPATCH_AVAILABLE or PYAUDIO_AVAILABLE
    
    def get_device_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current device information.
        
        Returns:
            Device information dictionary or None
        """
        return self.device_info
