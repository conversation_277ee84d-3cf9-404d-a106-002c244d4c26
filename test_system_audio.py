#!/usr/bin/env python3
"""
Test script for system audio capture functionality.

This script tests the system audio capture implementation to ensure it works
correctly with various audio sources and scenarios.
"""

import sys
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from RealtimeSTT import AudioToTextRecorder
        print("✓ RealtimeSTT imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import RealtimeSTT: {e}")
        return False
    
    try:
        import pyaudiowpatch
        print("✓ PyAudioWPatch imported successfully")
        return True
    except ImportError as e:
        print(f"⚠ PyAudioWPatch not available: {e}")
        print("System audio capture will not be available")
        return False

def test_system_audio_devices():
    """Test system audio device enumeration."""
    print("\nTesting system audio device enumeration...")
    
    try:
        from RealtimeSTT.system_audio_capture import SystemAudioCapture
        
        capture = SystemAudioCapture(debug_mode=True)
        
        # Test device enumeration
        devices = capture.get_available_loopback_devices()
        print(f"Found {len(devices)} loopback devices:")
        
        for i, device in enumerate(devices):
            print(f"  {i}: {device['name']}")
            print(f"     Index: {device['index']}, Channels: {device['maxInputChannels']}")
            print(f"     Sample Rate: {device['defaultSampleRate']}")
        
        # Test default device
        default_device = capture.get_default_loopback_device()
        if default_device:
            print(f"\nDefault loopback device: {default_device['name']}")
        else:
            print("\nNo default loopback device found")
        
        capture.cleanup()
        return len(devices) > 0
        
    except Exception as e:
        print(f"✗ Error testing device enumeration: {e}")
        return False

def test_recorder_initialization():
    """Test AudioToTextRecorder with system audio support."""
    print("\nTesting AudioToTextRecorder initialization...")
    
    try:
        from RealtimeSTT import AudioToTextRecorder
        
        # Test with system audio enabled
        config = {
            'model': 'tiny.en',
            'use_microphone': False,
            'use_system_audio': True,
            'system_audio_channels': 1,
            'spinner': False,
            'level': 40,  # ERROR level to reduce output
        }
        
        recorder = AudioToTextRecorder(**config)
        
        # Test audio source methods
        current_source = recorder.get_current_audio_source()
        print(f"Current audio source: {current_source}")
        
        # Test device listing
        devices = recorder.get_available_system_audio_devices()
        print(f"Available system audio devices: {len(devices)}")
        
        # Test source switching
        recorder.set_microphone(True)
        print(f"After switching to microphone: {recorder.get_current_audio_source()}")
        
        recorder.set_system_audio(True)
        print(f"After switching to system audio: {recorder.get_current_audio_source()}")
        
        recorder.toggle_audio_source()
        print(f"After toggle: {recorder.get_current_audio_source()}")
        
        recorder.shutdown()
        print("✓ Recorder initialization and control tests passed")
        return True
        
    except Exception as e:
        print(f"✗ Error testing recorder: {e}")
        return False

def test_basic_capture():
    """Test basic system audio capture functionality."""
    print("\nTesting basic system audio capture...")
    
    try:
        from RealtimeSTT.system_audio_capture import SystemAudioCapture
        
        # Create capture instance
        capture = SystemAudioCapture(
            sample_rate=16000,
            chunk_size=1024,
            channels=1,
            debug_mode=True
        )
        
        # Test setup
        if not capture.setup_device():
            print("✗ Failed to setup audio device")
            return False
        
        print("✓ Audio device setup successful")
        
        # Test capture start/stop
        if capture.start_capture():
            print("✓ Audio capture started")
            time.sleep(2)  # Capture for 2 seconds
            capture.stop_capture()
            print("✓ Audio capture stopped")
            return True
        else:
            print("✗ Failed to start audio capture")
            return False
            
    except Exception as e:
        print(f"✗ Error testing basic capture: {e}")
        return False

def test_audio_data_flow():
    """Test that audio data flows through the system correctly."""
    print("\nTesting audio data flow...")
    
    audio_data_received = False
    
    def audio_callback(data):
        nonlocal audio_data_received
        audio_data_received = True
        print(f"Received audio data: {len(data)} bytes")
    
    try:
        from RealtimeSTT.system_audio_capture import SystemAudioCapture
        
        capture = SystemAudioCapture(
            sample_rate=16000,
            chunk_size=1024,
            channels=1,
            on_audio_data=audio_callback,
            debug_mode=True
        )
        
        if capture.start_capture():
            print("Listening for audio data...")
            
            # Wait for audio data
            start_time = time.time()
            while not audio_data_received and (time.time() - start_time) < 5:
                time.sleep(0.1)
            
            capture.stop_capture()
            
            if audio_data_received:
                print("✓ Audio data flow test passed")
                return True
            else:
                print("⚠ No audio data received (may be normal if no audio is playing)")
                return True  # Not necessarily a failure
        else:
            print("✗ Failed to start capture for data flow test")
            return False
            
    except Exception as e:
        print(f"✗ Error testing audio data flow: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("=" * 60)
    print("System Audio Capture Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Device Enumeration", test_system_audio_devices),
        ("Recorder Initialization", test_recorder_initialization),
        ("Basic Capture", test_basic_capture),
        ("Audio Data Flow", test_audio_data_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠ Some tests failed. Check the output above for details.")
        return 1

def main():
    """Main entry point."""
    return run_all_tests()

if __name__ == "__main__":
    sys.exit(main())
