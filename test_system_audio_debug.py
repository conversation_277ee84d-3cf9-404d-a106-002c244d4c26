"""
Debug script to test system audio capture and diagnose issues.
"""

import time
import logging
import numpy as np
from RealtimeSTT.system_audio_capture import SystemAudioCapture

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_audio_levels():
    """Test if we're receiving audio data and check levels."""
    print("Testing system audio capture with level monitoring...")
    
    audio_data_count = 0
    max_level = 0
    
    def audio_callback(data):
        nonlocal audio_data_count, max_level
        audio_data_count += 1
        
        # Convert bytes to numpy array and calculate level
        try:
            audio_array = np.frombuffer(data, dtype=np.int16)
            level = np.max(np.abs(audio_array)) if len(audio_array) > 0 else 0
            max_level = max(max_level, level)
            
            if audio_data_count % 50 == 0:  # Print every 50 chunks
                print(f"Audio chunks received: {audio_data_count}, Current level: {level}, Max level: {max_level}")
                
        except Exception as e:
            print(f"Error processing audio data: {e}")
    
    try:
        capture = SystemAudioCapture(
            sample_rate=16000,
            chunk_size=1024,
            channels=1,
            on_audio_data=audio_callback,
            debug_mode=True
        )
        
        print("Available loopback devices:")
        devices = capture.get_available_loopback_devices()
        for i, device in enumerate(devices):
            print(f"  {i}: {device['name']} (Sample rate: {device['defaultSampleRate']})")
        
        print(f"\nStarting capture...")
        if capture.start_capture():
            print("✓ Capture started successfully")
            print("Play some audio through your speakers and watch for audio level changes...")
            print("Press Ctrl+C to stop")
            
            try:
                time.sleep(30)  # Listen for 30 seconds
            except KeyboardInterrupt:
                print("\nStopping...")
            
            capture.stop_capture()
            
            print(f"\nResults:")
            print(f"Total audio chunks received: {audio_data_count}")
            print(f"Maximum audio level detected: {max_level}")
            
            if audio_data_count == 0:
                print("❌ No audio data received - system audio capture may not be working")
            elif max_level == 0:
                print("⚠️  Audio data received but all silent - check if audio is actually playing")
            else:
                print("✓ Audio data and levels detected - system audio capture is working")
                
        else:
            print("❌ Failed to start capture")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_audio_levels()
