"""
Compatibility Testing Matrix for RealtimeSTT Ollama Integration

This module provides comprehensive tests to ensure backward compatibility
with existing configurations and proper fallback behavior.
"""

import unittest
import logging
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add the parent directory to the path to import RealtimeSTT
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from RealtimeSTT import AudioToTextRecorder
    from RealtimeSTT.config_migration import ConfigMigration
    from RealtimeSTT.ollama_client import OllamaClient
    IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Import error: {e}")
    IMPORTS_AVAILABLE = False


class TestOllamaCompatibility(unittest.TestCase):
    """Test suite for Ollama integration backward compatibility."""
    
    def setUp(self):
        """Set up test fixtures."""
        if not IMPORTS_AVAILABLE:
            self.skipTest("Required modules not available")
        
        # Disable logging during tests
        logging.disable(logging.CRITICAL)
        
        # Mock configurations for testing
        self.legacy_config = {
            'model': 'large-v2',
            'realtime_model_type': 'tiny.en',
            'language': 'en',
            'enable_realtime_transcription': True,
            'silero_sensitivity': 0.4,
            'webrtc_sensitivity': 3,
            'use_microphone': False,  # Disable microphone for testing
            'spinner': False,
        }
        
        self.ollama_config = {
            'model': 'gemma3:1b',
            'use_ollama': True,
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'fallback_to_external': True,
            'use_microphone': False,
            'spinner': False,
        }
        
        self.mixed_config = {
            'model': 'large-v2',
            'use_ollama': True,
            'ollama_model': 'gemma3:1b',
            'fallback_to_external': True,
            'use_microphone': False,
            'spinner': False,
        }
    
    def tearDown(self):
        """Clean up after tests."""
        logging.disable(logging.NOTSET)
    
    def test_legacy_config_migration(self):
        """Test that legacy configurations are properly migrated."""
        migration = ConfigMigration()
        migrated = migration.migrate_recorder_config(self.legacy_config)
        
        # Check that Ollama parameters were added
        self.assertIn('use_ollama', migrated)
        self.assertIn('ollama_model', migrated)
        self.assertIn('fallback_to_external', migrated)
        
        # Check that original parameters are preserved
        self.assertEqual(migrated['model'], 'large-v2')
        self.assertEqual(migrated['realtime_model_type'], 'tiny.en')
        
        # Check that fallback is enabled for compatibility
        self.assertTrue(migrated['fallback_to_external'])
    
    def test_whisper_model_detection(self):
        """Test detection of traditional Whisper models."""
        migration = ConfigMigration()
        
        whisper_models = [
            'tiny', 'tiny.en', 'base', 'base.en', 'small', 'small.en',
            'medium', 'medium.en', 'large-v1', 'large-v2', 'large-v3',
            'deepdml/faster-whisper-large-v3-turbo-ct2',
            'openai/whisper-large-v2'
        ]
        
        for model in whisper_models:
            self.assertTrue(migration._is_whisper_model(model), 
                          f"Failed to detect {model} as Whisper model")
        
        # Test non-Whisper models
        non_whisper_models = ['gemma3:1b', 'llama3.2:1b', 'custom-model']
        for model in non_whisper_models:
            self.assertFalse(migration._is_whisper_model(model),
                           f"Incorrectly detected {model} as Whisper model")
    
    def test_api_keys_optional(self):
        """Test that API keys are made optional during migration."""
        config_with_keys = {
            'model': 'large-v2',
            'openai_api_key': 'test-key',
            'azure_api_key': 'test-azure-key',
        }
        
        migration = ConfigMigration()
        migrated = migration.migrate_recorder_config(config_with_keys)
        
        # Check that API keys are marked as optional
        self.assertTrue(migrated.get('openai_api_key_optional', False))
        self.assertTrue(migrated.get('azure_api_key_optional', False))
    
    def test_compatibility_config_creation(self):
        """Test creation of compatibility configurations."""
        migration = ConfigMigration()
        
        # Test Ollama-first configuration
        ollama_config = migration.create_compatibility_config(
            use_ollama=True, preserve_external_apis=True
        )
        self.assertTrue(ollama_config['use_ollama'])
        self.assertEqual(ollama_config['model'], 'gemma3:1b')
        self.assertTrue(ollama_config['fallback_to_external'])
        
        # Test traditional configuration
        traditional_config = migration.create_compatibility_config(
            use_ollama=False, preserve_external_apis=True
        )
        self.assertFalse(traditional_config['use_ollama'])
        self.assertEqual(traditional_config['model'], 'large-v2')
    
    def test_config_validation(self):
        """Test configuration validation."""
        migration = ConfigMigration()
        
        # Test valid configuration
        valid_config = {
            'use_ollama': True,
            'ollama_model': 'gemma3:1b',
            'ollama_host': 'localhost:11434',
            'ollama_timeout': 30.0,
            'fallback_to_external': True,
        }
        warnings = migration.validate_migrated_config(valid_config)
        self.assertEqual(len(warnings), 0)
        
        # Test invalid configuration
        invalid_config = {
            'use_ollama': True,
            # Missing required parameters
        }
        warnings = migration.validate_migrated_config(invalid_config)
        self.assertGreater(len(warnings), 0)
    
    @patch('RealtimeSTT.ollama_client.OllamaClient')
    def test_ollama_fallback_behavior(self, mock_ollama_client):
        """Test fallback behavior when Ollama is unavailable."""
        # Mock Ollama client to simulate unavailability
        mock_client = Mock()
        mock_client.is_available.return_value = False
        mock_ollama_client.return_value = mock_client
        
        # Test that fallback works
        with patch('RealtimeSTT.audio_recorder.OLLAMA_AVAILABLE', True):
            try:
                recorder = AudioToTextRecorder(
                    use_ollama=True,
                    fallback_to_external=True,
                    use_microphone=False,
                    spinner=False
                )
                # Should not raise an exception due to fallback
                self.assertFalse(recorder.use_ollama)  # Should have fallen back
            except Exception as e:
                self.fail(f"Fallback failed: {e}")
    
    def test_mixed_configuration_handling(self):
        """Test handling of mixed Ollama/Whisper configurations."""
        migration = ConfigMigration()
        migrated = migration.migrate_recorder_config(self.mixed_config)
        
        # Should preserve both model specifications
        self.assertIn('model', migrated)
        self.assertIn('ollama_model', migrated)
        
        # Should detect potential conflicts
        warnings = migration.validate_migrated_config(migrated)
        conflict_warnings = [w for w in warnings if 'mismatch' in w.lower()]
        self.assertGreater(len(conflict_warnings), 0)
    
    @patch('RealtimeSTT.audio_recorder.OLLAMA_AVAILABLE', False)
    def test_ollama_unavailable_graceful_degradation(self):
        """Test graceful degradation when Ollama is completely unavailable."""
        try:
            recorder = AudioToTextRecorder(
                use_ollama=True,
                fallback_to_external=True,
                use_microphone=False,
                spinner=False
            )
            # Should work with fallback
            self.assertFalse(recorder.use_ollama)
        except Exception as e:
            self.fail(f"Should have gracefully degraded: {e}")
    
    @patch('RealtimeSTT.audio_recorder.OLLAMA_AVAILABLE', False)
    def test_ollama_unavailable_no_fallback_error(self):
        """Test error when Ollama unavailable and fallback disabled."""
        with self.assertRaises(ImportError):
            AudioToTextRecorder(
                use_ollama=True,
                fallback_to_external=False,
                use_microphone=False,
                spinner=False
            )
    
    def test_create_with_migration_method(self):
        """Test the create_with_migration class method."""
        # Test with legacy configuration
        try:
            recorder = AudioToTextRecorder.create_with_migration(
                self.legacy_config,
                use_microphone=False,
                spinner=False
            )
            # Should have Ollama parameters added
            self.assertTrue(hasattr(recorder, 'use_ollama'))
            self.assertTrue(hasattr(recorder, 'ollama_model'))
        except Exception as e:
            # May fail due to missing dependencies, but method should exist
            self.assertTrue(hasattr(AudioToTextRecorder, 'create_with_migration'))


class TestCompatibilityMatrix(unittest.TestCase):
    """Test matrix for different configuration scenarios."""
    
    def setUp(self):
        if not IMPORTS_AVAILABLE:
            self.skipTest("Required modules not available")
        logging.disable(logging.CRITICAL)
    
    def tearDown(self):
        logging.disable(logging.NOTSET)
    
    def test_compatibility_scenarios(self):
        """Test various compatibility scenarios."""
        scenarios = [
            {
                'name': 'Legacy Whisper Only',
                'config': {'model': 'large-v2', 'use_ollama': False},
                'expected_ollama': False
            },
            {
                'name': 'Ollama Primary with Fallback',
                'config': {'use_ollama': True, 'fallback_to_external': True},
                'expected_ollama': True
            },
            {
                'name': 'Mixed Configuration',
                'config': {'model': 'large-v2', 'use_ollama': True, 'ollama_model': 'gemma3:1b'},
                'expected_ollama': True
            }
        ]
        
        migration = ConfigMigration()
        
        for scenario in scenarios:
            with self.subTest(scenario=scenario['name']):
                config = scenario['config'].copy()
                config.update({'use_microphone': False, 'spinner': False})
                
                migrated = migration.migrate_recorder_config(config)
                self.assertEqual(
                    migrated.get('use_ollama', False),
                    scenario['expected_ollama'],
                    f"Failed scenario: {scenario['name']}"
                )


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
